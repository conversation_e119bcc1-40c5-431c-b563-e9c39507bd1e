generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id             String          @id @default(cuid())
  email          String          @unique
  name           String?
  password       String
  role           Role            @default(EMPLOYEE)
  isActive       Boolean         @default(true)
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  orders         Order[]
  stockMovements StockMovement[]

  @@map("users")
}

model RolePermission {
  id         String @id @default(cuid())
  role       Role
  permission String
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@unique([role, permission])
  @@map("role_permissions")
}

model Product {
  id             String          @id @default(cuid())
  name           String
  description    String?
  sku            String          @unique
  barcode        String?
  price          Float
  cost           Float
  quantity       Int             @default(0)
  minStock       Int             @default(0)
  categoryId     String?
  imageUrl       String?
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  orderItems     OrderItem[]
  category       Category?       @relation(fields: [categoryId], references: [id])
  stockMovements StockMovement[]

  @@map("products")
}

model Category {
  id          String     @id @default(cuid())
  name        String
  description String?
  parentId    String?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  parent      Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children    Category[] @relation("CategoryHierarchy")
  products    Product[]

  @@map("categories")
}

model Supplier {
  id             String          @id @default(cuid())
  name           String
  email          String?
  phone          String?
  address        String?
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  stockMovements StockMovement[]

  @@map("suppliers")
}

model Customer {
  id        String   @id @default(cuid())
  name      String
  email     String?
  phone     String?
  address   String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  orders    Order[]

  @@map("customers")
}

model Order {
  id             String          @id @default(cuid())
  orderNumber    String          @unique
  status         OrderStatus     @default(PENDING)
  totalAmount    Float
  customerId     String?
  customerName   String?
  userId         String
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  items          OrderItem[]
  customer       Customer?       @relation(fields: [customerId], references: [id])
  user           User            @relation(fields: [userId], references: [id])
  stockMovements StockMovement[]

  @@map("orders")
}

model OrderItem {
  id        String  @id @default(cuid())
  quantity  Int
  price     Float
  orderId   String
  productId String
  order     Order   @relation(fields: [orderId], references: [id])
  product   Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

model StockMovement {
  id         String       @id @default(cuid())
  type       MovementType
  quantity   Int
  productId  String
  userId     String
  supplierId String?
  orderId    String?
  notes      String?
  createdAt  DateTime     @default(now())
  order      Order?       @relation(fields: [orderId], references: [id])
  product    Product      @relation(fields: [productId], references: [id])
  supplier   Supplier?    @relation(fields: [supplierId], references: [id])
  user       User         @relation(fields: [userId], references: [id])

  @@map("stock_movements")
}

enum Role {
  ADMIN
  MANAGER
  EMPLOYEE
}

enum OrderStatus {
  PENDING
  CONFIRMED
  SHIPPED
  DELIVERED
  CANCELLED
}

enum MovementType {
  IN
  OUT
  ADJUSTMENT
}
