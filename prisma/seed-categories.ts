import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function seedCategories() {
  // Catégories principales
  const electronique = await prisma.category.create({
    data: {
      name: 'Électronique',
      description: 'Appareils et accessoires électroniques'
    }
  })

  const vetements = await prisma.category.create({
    data: {
      name: 'Vêtements',
      description: 'Vêtements et accessoires de mode'
    }
  })

  const alimentation = await prisma.category.create({
    data: {
      name: 'Alimentation',
      description: 'Produits alimentaires et boissons'
    }
  })

  // Sous-catégories
  await prisma.category.createMany({
    data: [
      {
        name: 'Smartphones',
        description: 'Téléphones mobiles et accessoires',
        parentId: electronique.id
      },
      {
        name: 'Ordinateurs',
        description: 'PC, laptops et accessoires informatiques',
        parentId: electronique.id
      },
      {
        name: 'Homme',
        description: 'Vêtements pour homme',
        parentId: vetements.id
      },
      {
        name: '<PERSON><PERSON>',
        description: 'Vêtements pour femme',
        parentId: vetements.id
      },
      {
        name: '<PERSON><PERSON>',
        description: 'Boissons diverses',
        parentId: alimentation.id
      },
      {
        name: 'Épicerie',
        description: 'Produits d\'épicerie générale',
        parentId: alimentation.id
      }
    ]
  })

  console.log('✅ Catégories créées avec succès')
}

seedCategories()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })