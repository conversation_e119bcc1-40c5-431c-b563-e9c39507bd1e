import { PrismaClient } from "@prisma/client";
import bcrypt from "bcryptjs";

const prisma = new PrismaClient();

async function main() {
  // Vérifier si l'utilisateur existe déjà
  const existingUser = await prisma.user.findUnique({
    where: { email: "<EMAIL>" },
  });

  let userId: string;

  if (!existingUser) {
    const hashedPassword = await bcrypt.hash("admin123", 12);

    const user = await prisma.user.create({
      data: {
        email: "<EMAIL>",
        name: "Administrateur",
        password: hashedPassword,
        role: "ADMIN",
      },
    });

    userId = user.id;
    console.log("Utilisateur admin créé:", {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
    });
  } else {
    userId = existingUser.id;
    console.log("Utilisateur admin déjà existant:", {
      id: existingUser.id,
      email: existingUser.email,
      name: existingUser.name,
      role: existingUser.role,
    });
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
