import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function seedSuppliers() {
  const suppliers = [
    {
      name: 'Distributeur <PERSON><PERSON><PERSON>',
      email: '<EMAIL>',
      phone: '+223 20 22 33 44',
      address: 'Zone Industrielle, Bamako, Mali'
    },
    {
      name: 'Import Export Sahel',
      email: '<EMAIL>',
      phone: '+223 76 55 44 33',
      address: 'Quartier du Fleuve, Bamako, Mali'
    },
    {
      name: 'Fournisseur Local',
      phone: '+223 65 43 21 09',
      address: 'Marché Central, Bamako, Mali'
    }
  ]

  for (const supplier of suppliers) {
    await prisma.supplier.create({
      data: supplier
    })
  }

  console.log('✅ Fournisseurs créés avec succès')
}

seedSuppliers()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })