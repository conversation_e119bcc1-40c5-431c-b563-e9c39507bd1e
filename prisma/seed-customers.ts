import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function seedCustomers() {
  const customers = [
    {
      name: '<PERSON>',
      email: 'jean.du<PERSON>@email.com',
      phone: '01 23 45 67 89',
      address: '123 Rue de la Paix, 75001 Paris'
    },
    {
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '01 98 76 54 32',
      address: '456 Avenue des Champs, 69000 Lyon'
    },
    {
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '04 56 78 90 12',
      address: '789 Boulevard du Commerce, 13000 Marseille'
    }
  ]

  for (const customer of customers) {
    await prisma.customer.create({
      data: customer
    })
  }

  console.log('✅ Clients créés avec succès')
}

seedCustomers()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })