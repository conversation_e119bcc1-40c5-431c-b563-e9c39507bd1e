import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function checkAndResetAdmin() {
  try {
    // Chercher l'utilisateur admin
    const admin = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (!admin) {
      console.log('Utilisateur admin non trouvé')
      return
    }

    console.log('Utilisateur admin trouvé:', {
      id: admin.id,
      email: admin.email,
      name: admin.name,
      role: admin.role,
      createdAt: admin.createdAt
    })

    // Tester le mot de passe actuel
    const isCurrentPasswordValid = await bcrypt.compare('admin123', admin.password)
    console.log('Mot de passe "admin123" valide:', isCurrentPasswordValid)

    // Si le mot de passe n'est pas valide, le réinitialiser
    if (!isCurrentPasswordValid) {
      console.log('Réinitialisation du mot de passe...')
      const newHashedPassword = await bcrypt.hash('admin123', 12)
      
      await prisma.user.update({
        where: { email: '<EMAIL>' },
        data: { password: newHashedPassword }
      })
      
      console.log('Mot de passe réinitialisé avec succès')
      
      // Vérifier le nouveau mot de passe
      const updatedAdmin = await prisma.user.findUnique({
        where: { email: '<EMAIL>' }
      })
      
      if (updatedAdmin) {
        const isNewPasswordValid = await bcrypt.compare('admin123', updatedAdmin.password)
        console.log('Nouveau mot de passe valide:', isNewPasswordValid)
      }
    }

  } catch (error) {
    console.error('Erreur:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkAndResetAdmin()