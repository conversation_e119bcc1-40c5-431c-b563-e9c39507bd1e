"use client"

import { useSession } from "next-auth/react"
import { hasPermission } from "@/lib/permissions"
import { ReactNode } from "react"

interface PermissionGuardProps {
  permission: string
  children: ReactNode
  fallback?: ReactNode
}

export function PermissionGuard({ permission, children, fallback = null }: PermissionGuardProps) {
  const { data: session } = useSession()
  
  if (!session?.user?.role) {
    return <>{fallback}</>
  }
  
  if (!hasPermission(session.user.role, permission)) {
    return <>{fallback}</>
  }
  
  return <>{children}</>
}

interface RoleGuardProps {
  roles: string[]
  children: ReactNode
  fallback?: ReactNode
}

export function RoleGuard({ roles, children, fallback = null }: RoleGuardProps) {
  const { data: session } = useSession()
  
  if (!session?.user?.role || !roles.includes(session.user.role)) {
    return <>{fallback}</>
  }
  
  return <>{children}</>
}