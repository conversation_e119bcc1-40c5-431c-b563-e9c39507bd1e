"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  <PERSON><PERSON>hart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Line,
  <PERSON><PERSON>hart,
  <PERSON>,
  Cell,
  Legend
} from "recharts"

interface ChartData {
  monthlySales: Array<{
    month: string
    orderCount: number
    revenue: number
  }>
  topProducts: Array<{
    name: string
    totalSold: number
    totalRevenue: number
  }>
  topCategories: Array<{
    name: string
    totalSold: number
    totalRevenue: number
  }>
  stockEvolution: Array<{
    day: string
    stockIn: number
    stockOut: number
  }>
  orderStatusDistribution: Array<{
    status: string
    count: number
    totalAmount: number
  }>
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D']

const STATUS_COLORS: { [key: string]: string } = {
  PENDING: '#FFA500',
  CONFIRMED: '#0088FE',
  SHIPPED: '#00C49F',
  DELIVERED: '#82CA9D',
  CANCELLED: '#FF8042'
}

const STATUS_LABELS: { [key: string]: string } = {
  PENDING: 'En attente',
  CONFIRMED: 'Confirmée',
  SHIPPED: 'Expédiée',
  DELIVERED: 'Livrée',
  CANCELLED: 'Annulée'
}

export default function DashboardCharts() {
  const [chartData, setChartData] = useState<ChartData>({
    monthlySales: [],
    topProducts: [],
    topCategories: [],
    stockEvolution: [],
    orderStatusDistribution: []
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchChartData()
  }, [])

  const fetchChartData = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/dashboard/charts')
      if (response.ok) {
        const data = await response.json()
        setChartData(data)
      }
    } catch (error) {
      console.error('Erreur lors du chargement des données pour les graphiques:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="space-y-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="bg-white/80 backdrop-blur-xl border border-gray-200/50 shadow-xl">
              <CardHeader>
                <div className="h-6 bg-gray-200 rounded animate-pulse"></div>
              </CardHeader>
              <CardContent>
                <div className="h-64 bg-gray-200 rounded animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  // Formater les montants en FCFA
  const formatCurrency = (value: number) => {
    return `${value.toFixed(0)} FCFA`
  }

  return (
    <div className="space-y-8">
      {/* Graphique 1: Ventes mensuelles */}
      <Card className="bg-white/80 backdrop-blur-xl border border-gray-200/50 shadow-xl">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-gray-900">Ventes mensuelles</CardTitle>
          <CardDescription>Évolution des ventes et revenus sur les 6 derniers mois</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={chartData.monthlySales}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis yAxisId="left" />
                <YAxis yAxisId="right" orientation="right" />
                <Tooltip 
                  formatter={(value, name) => [
                    name === 'orderCount' ? value : formatCurrency(Number(value)),
                    name === 'orderCount' ? 'Commandes' : 'Revenus'
                  ]}
                />
                <Legend 
                  formatter={(value) => value === 'orderCount' ? 'Nombre de commandes' : 'Revenus'}
                />
                <Bar yAxisId="left" dataKey="orderCount" fill="#8884d8" name="orderCount" />
                <Bar yAxisId="right" dataKey="revenue" fill="#82ca9d" name="revenue" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Graphique 2: Produits les plus vendus */}
        <Card className="bg-white/80 backdrop-blur-xl border border-gray-200/50 shadow-xl">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-gray-900">Produits les plus vendus</CardTitle>
            <CardDescription>Top 10 des produits par quantité vendue</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={chartData.topProducts} layout="horizontal">
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" />
                  <YAxis dataKey="name" type="category" width={80} />
                  <Tooltip formatter={(value) => [`${value} unités`, 'Quantité vendue']} />
                  <Bar dataKey="totalSold" fill="#0088FE" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Graphique 3: Catégories les plus populaires */}
        <Card className="bg-white/80 backdrop-blur-xl border border-gray-200/50 shadow-xl">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-gray-900">Catégories les plus populaires</CardTitle>
            <CardDescription>Top 8 des catégories par quantité vendue</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={chartData.topCategories}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${((percent || 0) * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="totalSold"
                  >
                    {chartData.topCategories.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`${value} unités`, 'Quantité vendue']} />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Graphique 4: Évolution du stock */}
        <Card className="bg-white/80 backdrop-blur-xl border border-gray-200/50 shadow-xl">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-gray-900">Mouvements de stock</CardTitle>
            <CardDescription>Entrées et sorties de stock sur les 30 derniers jours</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={chartData.stockEvolution}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="day" />
                  <YAxis />
                  <Tooltip />
                  <Legend formatter={(value) => value === 'stockIn' ? 'Entrées' : 'Sorties'} />
                  <Line 
                    type="monotone" 
                    dataKey="stockIn" 
                    stroke="#00C49F" 
                    strokeWidth={2}
                    name="stockIn"
                  />
                  <Line 
                    type="monotone" 
                    dataKey="stockOut" 
                    stroke="#FF8042" 
                    strokeWidth={2}
                    name="stockOut"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Graphique 5: Répartition des statuts de commandes */}
        <Card className="bg-white/80 backdrop-blur-xl border border-gray-200/50 shadow-xl">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-gray-900">Statuts des commandes</CardTitle>
            <CardDescription>Répartition des commandes par statut</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={chartData.orderStatusDistribution}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ status, percent }) => `${STATUS_LABELS[status]} ${((percent || 0) * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="count"
                  >
                    {chartData.orderStatusDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={STATUS_COLORS[entry.status] || '#8884d8'} />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value) => [
                      value,
                      'Nombre de commandes'
                    ]}
                  />
                  <Legend 
                    formatter={(value) => STATUS_LABELS[value] || value}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}