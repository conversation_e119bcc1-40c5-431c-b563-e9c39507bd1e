"use client";

import React from "react";
import { useSession, signOut } from "next-auth/react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { hasPermission } from "@/lib/permissions";
import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  Users,
  Truck,
  Settings,
  Activity,
  FolderTree,
  FileText,
  Shield,
  LogOut,
  ChevronUp,
  ChevronDown,
  Sparkles,
  TrendingUp,
  BarChart3,
} from "lucide-react";
import { ThemeToggle } from "@/components/theme-toggle";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarProvider,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useState } from "react";

// Types pour les éléments de navigation
type NavigationItem = {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  permission?: string;
  badge?: string | null;
  items?: { name: string; href: string }[];
};

const navigation: NavigationItem[] = [
  {
    name: "Tableau de bord",
    href: "/dashboard",
    icon: LayoutDashboard,
    description: "Vue d'ensemble de votre activité",
  },
  {
    name: "Produits",
    href: "/dashboard/products",
    icon: Package,
    permission: "products:view",
    description: "Gérer votre catalogue",
    badge: null,
  },
  {
    name: "Catégories",
    href: "/dashboard/categories",
    icon: FolderTree,
    permission: "categories:manage",
    description: "Organiser vos produits",
  },
  {
    name: "Commandes",
    href: "/dashboard/orders",
    icon: ShoppingCart,
    permission: "orders:view",
    description: "Suivre les ventes",
  },
  {
    name: "Stock",
    href: "/dashboard/stock",
    icon: Activity,
    permission: "stock:view",
    description: "Mouvements et niveaux",
  },
  {
    name: "Clients",
    href: "/dashboard/customers",
    icon: Users,
    permission: "customers:view",
    description: "Base de clients",
  },
  {
    name: "Fournisseurs",
    href: "/dashboard/suppliers",
    icon: Truck,
    permission: "suppliers:view",
    description: "Gérer les fournisseurs",
  },
  {
    name: "Rapports",
    href: "/dashboard/reports",
    icon: FileText,
    permission: "reports:view",
    description: "Analyses et rapports",
  },
  {
    name: "Utilisateurs",
    href: "/dashboard/users",
    icon: Users,
    permission: "users:manage",
    description: "Gestion des accès",
  },
  {
    name: "Permissions",
    href: "/dashboard/permissions",
    icon: Shield,
    permission: "users:manage",
    description: "Droits et rôles",
  },
  {
    name: "Paramètres",
    href: "/dashboard/settings",
    icon: Settings,
    permission: "settings:manage",
    description: "Configuration",
  },
];

function SidebarLogo() {
  const { state } = useSidebar();

  return (
    <SidebarHeader className="border-b border-border bg-gradient-to-r from-background to-accent/5">
      <div className="flex items-center gap-3 px-4 py-6">
        <div className="relative">
          <div className="w-10 h-10 bg-gradient-to-br from-primary to-primary/80 rounded-xl flex items-center justify-center shadow-lg">
            <span className="text-primary-foreground font-bold text-lg">
              SP
            </span>
          </div>
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-background animate-pulse"></div>
        </div>
        {state === "expanded" && (
          <>
            <div className="flex-1">
              <h1 className="text-xl font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                Stocks Mali
              </h1>
              <p className="text-xs text-muted-foreground font-medium">
                Gestion Intelligente
              </p>
            </div>
            <div className="flex items-center gap-2">
              <ThemeToggle />
              <Sparkles className="w-4 h-4 text-primary animate-pulse" />
            </div>
          </>
        )}
        {state === "collapsed" && (
          <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2">
            <ThemeToggle />
          </div>
        )}
      </div>
    </SidebarHeader>
  );
}

function SidebarUserMenu() {
  const { data: session } = useSession();
  const { state } = useSidebar();
  const [dropdownOpen, setDropdownOpen] = useState(false);

  // Fermer le dropdown quand le sidebar est réduit
  React.useEffect(() => {
    if (state === "collapsed") {
      setDropdownOpen(false);
    }
  }, [state]);

  if (!session?.user) return null;

  return (
    <SidebarFooter className="border-t border-border bg-gradient-to-r from-accent/5 to-background">
      <div className="px-3 py-3">
        <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className={cn(
                "w-full justify-start h-auto p-3 hover:bg-sidebar-accent/50 transition-all duration-200",
                state === "collapsed" && "justify-center px-2",
              )}
            >
              <div className="flex items-center gap-3 w-full">
                <div className="relative">
                  <Avatar className="w-9 h-9 border-2 border-border shadow-sm">
                    <AvatarImage
                      src={session.user.image || ""}
                      alt={session.user.name || ""}
                    />
                    <AvatarFallback className="bg-gradient-to-br from-primary to-primary/80 text-primary-foreground font-semibold">
                      {session.user.name?.charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border-2 border-background"></div>
                </div>
                {state === "expanded" && (
                  <div className="flex-1 text-left">
                    <p className="text-sm font-semibold text-foreground truncate">
                      {session.user.name}
                    </p>
                    <p className="text-xs text-muted-foreground flex items-center gap-1">
                      <span className="w-1.5 h-1.5 bg-primary rounded-full"></span>
                      {session.user.role}
                    </p>
                  </div>
                )}
                {state === "expanded" && (
                  <ChevronUp
                    className={cn(
                      "w-4 h-4 text-muted-foreground transition-transform duration-200",
                      dropdownOpen && "rotate-180",
                    )}
                  />
                )}
              </div>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            align="end"
            className="w-56"
            side={state === "collapsed" ? "right" : "top"}
            sideOffset={state === "collapsed" ? 10 : 0}
          >
            <DropdownMenuLabel className="font-normal">
              <div className="flex flex-col space-y-1">
                <p className="text-sm font-medium leading-none">
                  {session.user.name}
                </p>
                <p className="text-xs leading-none text-muted-foreground">
                  {session.user.email}
                </p>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <TrendingUp className="mr-2 h-4 w-4" />
              <span>Mon activité</span>
            </DropdownMenuItem>
            <DropdownMenuItem>
              <BarChart3 className="mr-2 h-4 w-4" />
              <span>Statistiques</span>
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Settings className="mr-2 h-4 w-4" />
              <span>Paramètres</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => signOut({ callbackUrl: "/auth/signin" })}
              className="text-red-600 focus:text-red-600"
            >
              <LogOut className="mr-2 h-4 w-4" />
              <span>Déconnexion</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </SidebarFooter>
  );
}

function SidebarNavigation() {
  const { data: session } = useSession();
  const pathname = usePathname();
  const { state } = useSidebar();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const filteredNavigation = navigation.filter(
    (item) =>
      !item.permission ||
      hasPermission((session?.user?.role as string) || "", item.permission),
  );

  const toggleExpanded = (itemName: string) => {
    setExpandedItems((prev) =>
      prev.includes(itemName)
        ? prev.filter((name) => name !== itemName)
        : [...prev, itemName],
    );
  };

  // Fermer tous les menus déroulants quand le sidebar est réduit
  React.useEffect(() => {
    if (state === "collapsed") {
      setExpandedItems([]);
    }
  }, [state]);

  return (
    <SidebarContent className="px-3 py-4">
      <SidebarGroup>
        {state === "expanded" && (
          <SidebarGroupLabel className="text-xs font-semibold text-muted-foreground px-2 mb-2">
            Navigation
          </SidebarGroupLabel>
        )}
        <SidebarGroupContent>
          <SidebarMenu className="space-y-1">
            {filteredNavigation.map((item) => {
              const isActive =
                pathname === item.href ||
                (item.items &&
                  item.items.some((subItem) => pathname === subItem.href));
              const isExpanded = expandedItems.includes(item.name);

              return (
                <SidebarMenuItem key={item.name}>
                  {item.items ? (
                    <>
                      <SidebarMenuButton
                        isActive={isActive}
                        tooltip={item.name}
                        className={cn(
                          "h-10 transition-all duration-200 hover:bg-accent/50 group",
                          isActive && "bg-accent shadow-sm",
                        )}
                        onClick={() =>
                          state === "expanded" && toggleExpanded(item.name)
                        }
                      >
                        <item.icon
                          className={cn(
                            "w-5 h-5 transition-all duration-200",
                            isActive
                              ? "text-accent-foreground"
                              : "text-muted-foreground group-hover:text-accent-foreground",
                          )}
                        />
                        <span className="font-medium">{item.name}</span>
                        {item.badge && (
                          <Badge
                            variant="destructive"
                            className="ml-auto text-xs px-1.5 py-0.5 h-5"
                          >
                            {item.badge}
                          </Badge>
                        )}
                        {state === "expanded" && (
                          <ChevronDown
                            className={cn(
                              "ml-auto w-4 h-4 text-muted-foreground transition-transform duration-200",
                              isExpanded && "rotate-180",
                            )}
                          />
                        )}
                      </SidebarMenuButton>
                      {state === "expanded" && isExpanded && (
                        <SidebarMenuSub className="ml-8 space-y-1">
                          {item.items.map((subItem) => (
                            <SidebarMenuSubItem key={subItem.name}>
                              <SidebarMenuSubButton
                                asChild
                                isActive={pathname === subItem.href}
                                className="h-8 transition-all duration-200 hover:bg-accent/30"
                              >
                                <Link href={subItem.href}>
                                  <span className="text-sm">
                                    {subItem.name}
                                  </span>
                                </Link>
                              </SidebarMenuSubButton>
                            </SidebarMenuSubItem>
                          ))}
                        </SidebarMenuSub>
                      )}
                    </>
                  ) : (
                    <SidebarMenuButton
                      asChild
                      isActive={isActive}
                      tooltip={item.name}
                      className={cn(
                        "h-10 transition-all duration-200 hover:bg-accent/50 group relative overflow-hidden",
                        isActive && "bg-accent shadow-sm",
                      )}
                    >
                      <Link href={item.href}>
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
                        <item.icon
                          className={cn(
                            "w-5 h-5 transition-all duration-200 relative z-10",
                            isActive
                              ? "text-accent-foreground"
                              : "text-muted-foreground group-hover:text-accent-foreground",
                          )}
                        />
                        <span className="font-medium relative z-10">
                          {item.name}
                        </span>
                        {item.badge && (
                          <Badge
                            variant="destructive"
                            className="ml-auto text-xs px-1.5 py-0.5 h-5 relative z-10"
                          >
                            {item.badge}
                          </Badge>
                        )}
                      </Link>
                    </SidebarMenuButton>
                  )}
                </SidebarMenuItem>
              );
            })}
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>
    </SidebarContent>
  );
}

export function AppSidebar({ children }: { children?: React.ReactNode }) {
  return (
    <SidebarProvider>
      <Sidebar variant="inset" collapsible="icon">
        <SidebarLogo />
        <SidebarNavigation />
        <SidebarUserMenu />
      </Sidebar>
      {children}
    </SidebarProvider>
  );
}

export { SidebarTrigger };
