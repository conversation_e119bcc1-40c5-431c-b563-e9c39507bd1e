import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

const createOrderSchema = z.object({
  customerId: z.string().optional(),
  customerName: z.string().optional(),
  items: z.array(z.object({
    productId: z.string(),
    quantity: z.number().positive(),
    price: z.number().positive()
  })).min(1, 'Au moins un produit requis')
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const search = searchParams.get('search') || ''
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '12')
    
    const skip = (page - 1) * limit
    
    const where: Record<string, unknown> = {}
    if (status) where.status = status
    if (search) {
      where.OR = [
        { orderNumber: { contains: search, mode: 'insensitive' as const } },
        { customerName: { contains: search, mode: 'insensitive' as const } },
        { customer: { name: { contains: search, mode: 'insensitive' as const } } }
      ]
    }
    
    const [orders, total] = await Promise.all([
      prisma.order.findMany({
        where,
        include: {
          customer: true,
          user: true,
          items: {
            include: {
              product: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.order.count({ where })
    ])

    return NextResponse.json({ 
      orders,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Erreur lors de la récupération des commandes:', error)
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Non authentifié' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createOrderSchema.parse(body)
    
    const userId = session.user.id
    
    // Générer numéro de commande
    const orderNumber = `CMD-${Date.now()}`
    
    // Calculer le montant total
    const totalAmount = validatedData.items.reduce((sum, item) => 
      sum + (item.quantity * item.price), 0
    )
    
    const orderData: {
      orderNumber: string;
      totalAmount: number;
      userId: string;
      items: { create: typeof validatedData.items };
      customerId?: string;
      customerName?: string;
    } = {
      orderNumber,
      totalAmount,
      userId, // Utilisation de l'ID récupéré dynamiquement
      items: {
        create: validatedData.items
      }
    }
    
    if (validatedData.customerId) {
      orderData.customerId = validatedData.customerId
    } else if (validatedData.customerName) {
      orderData.customerName = validatedData.customerName
    }
    
    const order = await prisma.order.create({
      data: orderData,
      include: {
        customer: true,
        items: {
          include: {
            product: true
          }
        }
      }
    })

    return NextResponse.json(order, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation failed', details: error.issues }, { status: 400 })
    }
    console.error('Erreur lors de la création de la commande:', error)
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 })
  }
}