import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params
    const order = await prisma.order.findUnique({
      where: { id },
      include: {
        customer: true,
        items: {
          include: {
            product: true
          }
        }
      }
    })

    if (!order) {
      return NextResponse.json({ error: 'Commande non trouvée' }, { status: 404 })
    }

    const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Facture ${order.orderNumber}</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .company { font-size: 24px; font-weight: bold; color: #2563eb; }
        .invoice-info { display: flex; justify-content: space-between; margin-bottom: 30px; }
        .customer-info { margin-bottom: 30px; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; font-weight: bold; }
        .total { text-align: right; font-size: 18px; font-weight: bold; }
        .footer { margin-top: 40px; text-align: center; color: #666; }
        @media print { body { margin: 0; } }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="company">Stocks Mali</div>
        <div>Système de Gestion de Stock</div>
      </div>
      
      <div class="invoice-info">
        <div>
          <strong>Facture N°:</strong> ${order.orderNumber}<br>
          <strong>Date:</strong> ${new Date(order.createdAt).toLocaleDateString('fr-FR')}
        </div>
        <div>
          <strong>Statut:</strong> ${order.status === 'DELIVERED' ? 'Livrée' : 
                                   order.status === 'CONFIRMED' ? 'Confirmée' : 
                                   order.status === 'PENDING' ? 'En attente' : order.status}
        </div>
      </div>
      
      <div class="customer-info">
        <strong>Client:</strong><br>
        ${order.customer ? order.customer.name : (order.customerName || 'Client anonyme')}<br>
        ${order.customer?.email ? order.customer.email + '<br>' : ''}
        ${order.customer?.phone ? order.customer.phone : ''}
      </div>
      
      <table>
        <thead>
          <tr>
            <th>Produit</th>
            <th>SKU</th>
            <th>Quantité</th>
            <th>Prix unitaire</th>
            <th>Total</th>
          </tr>
        </thead>
        <tbody>
          ${order.items.map(item => `
            <tr>
              <td>${item.product.name}</td>
              <td>${item.product.sku}</td>
              <td>${item.quantity}</td>
              <td>${item.price.toFixed(2)} FCFA</td>
              <td>${(item.quantity * item.price).toFixed(2)} FCFA</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
      
      <div class="total">
        <strong>Total général: ${order.totalAmount.toFixed(2)} FCFA</strong>
      </div>
      
      <div class="footer">
        <p>Merci pour votre confiance !</p>
        <p>Stocks Mali - Gestion de Stock Professionnelle</p>
      </div>
      
      <script>
        window.onload = function() {
          window.print();
          window.onafterprint = function() {
            window.close();
          }
        }
      </script>
    </body>
    </html>
    `

    return new NextResponse(html, {
      headers: {
        'Content-Type': 'text/html',
      },
    })
  } catch (error) {
    console.error('Erreur lors de la génération de la facture:', error)
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 })
  }
}