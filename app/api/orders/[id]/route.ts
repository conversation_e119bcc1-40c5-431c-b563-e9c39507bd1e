import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateOrderSchema = z.object({
  status: z.enum(['PENDING', 'CONFIRMED', 'SHIPPED', 'DELIVERED', 'CANCELLED']).optional(),
  customerId: z.string().optional(),
  customerName: z.string().optional()
})

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params
    const order = await prisma.order.findUnique({
      where: { id },
      include: {
        customer: true,
        user: true,
        items: {
          include: {
            product: true
          }
        }
      }
    })

    if (!order) {
      return NextResponse.json({ error: 'Commande non trouvée' }, { status: 404 })
    }

    return NextResponse.json(order)
  } catch (error) {
    console.error('Erreur lors de la récupération de la commande:', error)
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 })
  }
}

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params
    const body = await request.json()
    const validatedData = updateOrderSchema.parse(body)
    
    // Si on confirme la commande, décrémenter le stock
    if (validatedData.status === 'CONFIRMED') {
      const order = await prisma.order.findUnique({
        where: { id },
        include: { items: true }
      })
      
      if (order && order.status === 'PENDING') {
        // Vérifier le stock disponible
        for (const item of order.items) {
          const product = await prisma.product.findUnique({
            where: { id: item.productId }
          })
          
          if (!product || product.quantity < item.quantity) {
            return NextResponse.json({ 
              error: `Stock insuffisant pour ${product?.name || 'le produit'}` 
            }, { status: 400 })
          }
        }
        
        // Décrémenter le stock
        for (const item of order.items) {
          await prisma.product.update({
            where: { id: item.productId },
            data: { quantity: { decrement: item.quantity } }
          })
          
          // Créer un mouvement de stock
          await prisma.stockMovement.create({
            data: {
              type: 'OUT',
              quantity: item.quantity,
              productId: item.productId,
              userId: order.userId,
              orderId: order.id,
              notes: `Vente - Commande ${order.orderNumber}`
            }
          })
        }
      }
    }

    const updatedOrder = await prisma.order.update({
      where: { id },
      data: validatedData,
      include: {
        customer: true,
        items: {
          include: {
            product: true
          }
        }
      }
    })

    return NextResponse.json(updatedOrder)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation failed', details: error.issues }, { status: 400 })
    }
    console.error('Erreur lors de la mise à jour de la commande:', error)
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params
    await prisma.order.delete({
      where: { id }
    })

    return NextResponse.json({ message: 'Commande supprimée' })
  } catch (error) {
    console.error('Erreur lors de la suppression de la commande:', error)
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 })
  }
}