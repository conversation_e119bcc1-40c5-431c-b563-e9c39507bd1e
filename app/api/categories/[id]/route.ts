import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateCategorySchema = z.object({
  name: z.string().min(1, 'Le nom est requis'),
  description: z.string().optional(),
  parentId: z.string().optional()
})

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params
    const body = await request.json()
    const validatedData = updateCategorySchema.parse(body)
    
    const category = await prisma.category.update({
      where: { id },
      data: {
        ...validatedData,
        parentId: validatedData.parentId || null
      },
      include: {
        parent: true,
        _count: {
          select: { products: true }
        }
      }
    })

    return NextResponse.json(category)
  } catch (error: unknown) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation failed', details: error.issues }, { status: 400 })
    }
    console.error('Erreur lors de la mise à jour de la catégorie:', error)
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params
    // Vérifier s'il y a des produits dans cette catégorie
    const productsCount = await prisma.product.count({
      where: { categoryId: id }
    })
    
    if (productsCount > 0) {
      return NextResponse.json({ 
        error: 'Impossible de supprimer une catégorie contenant des produits' 
      }, { status: 400 })
    }
    
    await prisma.category.delete({
      where: { id }
    })

    return NextResponse.json({ message: 'Catégorie supprimée' })
  } catch (error) {
    console.error('Erreur lors de la suppression de la catégorie:', error)
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 })
  }
}