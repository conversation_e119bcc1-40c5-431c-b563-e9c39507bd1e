import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function GET() {
  try {
    const permissions = await prisma.rolePermission.findMany()
    const rolePermissions: {[key: string]: string[]} = {
      ADMIN: [],
      MANAGER: [],
      EMPLOYEE: []
    }
    
    permissions.forEach(p => {
      rolePermissions[p.role].push(p.permission)
    })
    
    return NextResponse.json(rolePermissions)
  } catch {
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (session?.user?.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Non autorisé' }, { status: 403 })
    }

    const { rolePermissions } = await request.json()
    
    await prisma.rolePermission.deleteMany()
    
    const data: Array<{ role: 'ADMIN' | 'MANAGER' | 'EMPLOYEE'; permission: string }> = []
    for (const [role, permissions] of Object.entries(rolePermissions)) {
      for (const permission of permissions as string[]) {
        data.push({ role: role as 'ADMIN' | 'MANAGER' | 'EMPLOYEE', permission })
      }
    }
    
    await prisma.rolePermission.createMany({ data })
    
    return NextResponse.json({ success: true })
  } catch {
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 })
  }
}