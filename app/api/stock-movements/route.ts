import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

const createMovementSchema = z.object({
  type: z.enum(['IN', 'OUT', 'ADJUSTMENT']),
  quantity: z.number().positive(),
  productId: z.string(),
  supplierId: z.string().optional(),
  notes: z.string().optional()
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const productId = searchParams.get('productId')
    const type = searchParams.get('type')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    
    const skip = (page - 1) * limit
    
    const where: {
      productId?: string;
      type?: 'IN' | 'OUT' | 'ADJUSTMENT';
    } = {}
    if (productId) where.productId = productId
    if (type) where.type = type as 'IN' | 'OUT' | 'ADJUSTMENT'
    
    const [movements, total] = await Promise.all([
      prisma.stockMovement.findMany({
        where,
        include: {
          product: true,
          supplier: true,
          user: true,
          order: true
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.stockMovement.count({ where })
    ])

    return NextResponse.json({
      movements,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Erreur lors de la récupération des mouvements:', error)
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Non authentifié' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createMovementSchema.parse(body)
    
    // Vérifier que le produit existe
    const product = await prisma.product.findUnique({
      where: { id: validatedData.productId }
    })
    
    if (!product) {
      return NextResponse.json({ error: 'Produit non trouvé' }, { status: 404 })
    }
    
    // Calculer la nouvelle quantité
    let newQuantity = product.quantity
    if (validatedData.type === 'IN') {
      newQuantity += validatedData.quantity
    } else if (validatedData.type === 'OUT') {
      newQuantity -= validatedData.quantity
      if (newQuantity < 0) {
        return NextResponse.json({ error: 'Stock insuffisant' }, { status: 400 })
      }
    } else if (validatedData.type === 'ADJUSTMENT') {
      newQuantity = validatedData.quantity
    }
    
    // Transaction pour créer le mouvement et mettre à jour le stock
    const result = await prisma.$transaction(async (tx) => {
      // Créer le mouvement
      const movement = await tx.stockMovement.create({
        data: {
          ...validatedData,
          userId: session.user.id!
        },
        include: {
          product: true,
          supplier: true,
          user: true
        }
      })
      
      // Mettre à jour le stock du produit
      await tx.product.update({
        where: { id: validatedData.productId },
        data: { quantity: newQuantity }
      })
      
      return movement
    })

    return NextResponse.json(result, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation failed', details: error.issues }, { status: 400 })
    }
    console.error('Erreur lors de la création du mouvement:', error)
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 })
  }
}