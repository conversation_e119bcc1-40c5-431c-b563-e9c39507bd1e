import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateCustomerSchema = z.object({
  name: z.string().min(1, 'Le nom est requis'),
  email: z.string().email().optional().or(z.literal('')),
  phone: z.string().optional(),
  address: z.string().optional()
})

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params
    const body = await request.json()
    const validatedData = updateCustomerSchema.parse(body)
    
    const customer = await prisma.customer.update({
      where: { id },
      data: {
        ...validatedData,
        email: validatedData.email || null
      }
    })

    return NextResponse.json(customer)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation failed', details: error.issues }, { status: 400 })
    }
    console.error('Erreur lors de la mise à jour du client:', error)
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params
    await prisma.customer.delete({
      where: { id }
    })

    return NextResponse.json({ message: 'Client supprimé' })
  } catch (error) {
    console.error('Erreur lors de la suppression du client:', error)
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 })
  }
}