import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    // Graphique 1: V<PERSON><PERSON> men<PERSON> (6 derniers mois)
    const sixMonthsAgo = new Date()
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6)
    
    const monthlySales = await prisma.$queryRaw`
      SELECT 
        DATE_TRUNC('month', "createdAt") as month,
        COUNT(*) as orderCount,
        COALESCE(SUM("totalAmount"), 0) as revenue
      FROM "orders" 
      WHERE "createdAt" >= ${sixMonthsAgo}
        AND status IN ('CONFIRMED', 'DELIVERED')
      GROUP BY DATE_TRUNC('month', "createdAt")
      ORDER BY month ASC
    ` as Array<{
      month: Date
      orderCount: number
      revenue: number
    }>

    // Graphique 2: Produits les plus vendus
    const topProducts = await prisma.$queryRaw`
      SELECT 
        p.name,
        p.id,
        SUM(oi.quantity) as totalSold,
        SUM(oi.quantity * oi.price) as totalRevenue
      FROM "order_items" oi
      JOIN "products" p ON oi."productId" = p.id
      JOIN "orders" o ON oi."orderId" = o.id
      WHERE o.status IN ('CONFIRMED', 'DELIVERED')
      GROUP BY p.id, p.name
      ORDER BY totalSold DESC
      LIMIT 10
    ` as Array<{
      name: string
      id: string
      totalSold: number
      totalRevenue: number
    }>

    // Graphique 3: Catégories les plus populaires
    const topCategories = await prisma.$queryRaw`
      SELECT 
        c.name,
        c.id,
        SUM(oi.quantity) as totalSold,
        SUM(oi.quantity * oi.price) as totalRevenue
      FROM "order_items" oi
      JOIN "products" p ON oi."productId" = p.id
      JOIN "categories" c ON p."categoryId" = c.id
      JOIN "orders" o ON oi."orderId" = o.id
      WHERE o.status IN ('CONFIRMED', 'DELIVERED')
        AND c.id IS NOT NULL
      GROUP BY c.id, c.name
      ORDER BY totalSold DESC
      LIMIT 8
    ` as Array<{
      name: string
      id: string
      totalSold: number
      totalRevenue: number
    }>

    // Graphique 4: Évolution du stock (derniers 30 jours)
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
    
    const stockEvolution = await prisma.$queryRaw`
      SELECT 
        DATE_TRUNC('day', "createdAt") as day,
        SUM(CASE WHEN type = 'IN' THEN quantity ELSE 0 END) as stockIn,
        SUM(CASE WHEN type = 'OUT' THEN quantity ELSE 0 END) as stockOut
      FROM "stock_movements"
      WHERE "createdAt" >= ${thirtyDaysAgo}
      GROUP BY DATE_TRUNC('day', "createdAt")
      ORDER BY day ASC
    ` as Array<{
      day: Date
      stockIn: number
      stockOut: number
    }>

    // Graphique 5: Répartition des statuts de commandes
    const orderStatusDistribution = await prisma.$queryRaw`
      SELECT 
        status,
        COUNT(*) as count,
        COALESCE(SUM("totalAmount"), 0) as totalAmount
      FROM "orders"
      GROUP BY status
      ORDER BY count DESC
    ` as Array<{
      status: string
      count: number
      totalAmount: number
    }>

    // Formater les données pour les graphiques
    const formattedMonthlySales = monthlySales.map(item => ({
      month: new Date(item.month).toLocaleDateString('fr-FR', { month: 'short', year: 'numeric' }),
      orderCount: Number(item.orderCount),
      revenue: Number(item.revenue)
    }))

    const formattedTopProducts = topProducts.map(item => ({
      name: item.name,
      totalSold: Number(item.totalSold),
      totalRevenue: Number(item.totalRevenue)
    }))

    const formattedTopCategories = topCategories.map(item => ({
      name: item.name,
      totalSold: Number(item.totalSold),
      totalRevenue: Number(item.totalRevenue)
    }))

    const formattedStockEvolution = stockEvolution.map(item => ({
      day: new Date(item.day).toLocaleDateString('fr-FR', { day: 'numeric', month: 'short' }),
      stockIn: Number(item.stockIn),
      stockOut: Number(item.stockOut)
    }))

    const formattedOrderStatus = orderStatusDistribution.map(item => ({
      status: item.status,
      count: Number(item.count),
      totalAmount: Number(item.totalAmount)
    }))

    return NextResponse.json({
      monthlySales: formattedMonthlySales,
      topProducts: formattedTopProducts,
      topCategories: formattedTopCategories,
      stockEvolution: formattedStockEvolution,
      orderStatusDistribution: formattedOrderStatus
    })
  } catch (error) {
    console.error('Erreur lors de la récupération des données pour les graphiques:', error)
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 })
  }
}