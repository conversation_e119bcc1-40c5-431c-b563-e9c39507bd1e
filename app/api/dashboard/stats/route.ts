import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const today = new Date()
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate())
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)
    
    const [
      totalProducts,
      totalOrders,
      totalCustomers,
      pendingOrders,
      lowStockProducts,
      todayOrders,
      monthlyRevenue,
      todayRevenue
    ] = await Promise.all([
      prisma.product.count(),
      prisma.order.count(),
      prisma.customer.count(),
      prisma.order.count({ where: { status: 'PENDING' } }),
      prisma.product.count({ where: { quantity: { lte: 5 } } }),
      prisma.order.count({ where: { createdAt: { gte: startOfDay } } }),
      prisma.order.aggregate({
        where: {
          createdAt: { gte: startOfMonth },
          status: { in: ['CONFIRMED', 'DELIVERED'] }
        },
        _sum: { totalAmount: true }
      }),
      prisma.order.aggregate({
        where: {
          createdAt: { gte: startOfDay },
          status: { in: ['CONFIRMED', 'DELIVERED'] }
        },
        _sum: { totalAmount: true }
      })
    ])

    return NextResponse.json({
      totalProducts,
      totalOrders,
      totalCustomers,
      pendingOrders,
      lowStockProducts,
      todayOrders,
      monthlyRevenue: monthlyRevenue._sum.totalAmount || 0,
      todayRevenue: todayRevenue._sum.totalAmount || 0
    })
  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques:', error)
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 })
  }
}