import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateProductSchema = z.object({
  name: z.string().min(1, 'Le nom est requis').optional(),
  description: z.string().optional(),
  sku: z.string().min(1, 'Le SKU est requis').optional(),
  barcode: z.string().optional(),
  price: z.number().positive('Le prix doit être positif').optional(),
  cost: z.number().positive('Le coût doit être positif').optional(),
  quantity: z.number().int().min(0, 'La quantité doit être positive').optional(),
  minStock: z.number().int().min(0, 'Le stock minimum doit être positif').optional(),
  categoryId: z.string().optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const product = await prisma.product.findUnique({
      where: { id },
      include: {
        category: true,
      },
    })

    if (!product) {
      return NextResponse.json(
        { error: 'Produit non trouvé' },
        { status: 404 }
      )
    }

    return NextResponse.json(product)
  } catch (error) {
    console.error('Erreur lors de la récupération du produit:', error)
    return NextResponse.json(
      { error: 'Erreur serveur' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    
    // Convertir les chaînes en nombres si elles existent et gérer les champs vides
    const processedData = { ...body }
    if (body.price !== undefined) processedData.price = parseFloat(body.price) || 0
    if (body.cost !== undefined) processedData.cost = parseFloat(body.cost) || 0
    if (body.quantity !== undefined) processedData.quantity = parseInt(body.quantity) || 0
    if (body.minStock !== undefined) processedData.minStock = parseInt(body.minStock) || 0
    if (body.categoryId !== undefined) {
      processedData.categoryId = body.categoryId && body.categoryId.trim() !== "" ? body.categoryId : null
    }
    
    const validatedData = updateProductSchema.parse(processedData)

    const product = await prisma.product.update({
      where: { id },
      data: validatedData,
      include: {
        category: true,
      },
    })

    return NextResponse.json(product)
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('Erreur de validation:', error.issues)
      return NextResponse.json(
        { error: 'Validation failed', details: error.issues },
        { status: 400 }
      )
    }

    console.error('Erreur lors de la mise à jour du produit:', error)
    return NextResponse.json(
      { error: 'Erreur serveur' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    await prisma.product.delete({
      where: { id },
    })

    return NextResponse.json({ message: 'Produit supprimé avec succès' })
  } catch (error) {
    console.error('Erreur lors de la suppression du produit:', error)
    return NextResponse.json(
      { error: 'Erreur serveur' },
      { status: 500 }
    )
  }
}