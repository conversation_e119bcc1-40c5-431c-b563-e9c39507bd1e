import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { hash } from 'bcryptjs'
import { z } from 'zod'

const updateUserSchema = z.object({
  name: z.string().min(1, 'Le nom est requis').optional(),
  email: z.string().email('Email invalide').optional(),
  password: z.string().min(6, 'Mot de passe minimum 6 caractères').optional(),
  role: z.enum(['ADMIN', 'MANAGER', 'EMPLOYEE']).optional(),
  isActive: z.boolean().optional()
})

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params
    const body = await request.json()
    const validatedData = updateUserSchema.parse(body)
    
    // Préparer les données à mettre à jour
    const updateData: {
      name?: string;
      email?: string;
      password?: string;
      role?: 'ADMIN' | 'MANAGER' | 'EMPLOYEE';
      isActive?: boolean;
    } = { ...validatedData }
    
    // Hasher le mot de passe si fourni
    if (validatedData.password) {
      updateData.password = await hash(validatedData.password, 12)
    }
    
    const user = await prisma.user.update({
      where: { id },
      data: updateData,
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        isActive: true,
        createdAt: true
      }
    })

    return NextResponse.json(user)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation failed', details: error.issues }, { status: 400 })
    }
    console.error('Erreur lors de la mise à jour de l\'utilisateur:', error)
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params
    // Vérifier si l'utilisateur a des données liées
    const userWithData = await prisma.user.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            orders: true,
            stockMovements: true
          }
        }
      }
    })
    
    if (!userWithData) {
      return NextResponse.json({ error: 'Utilisateur non trouvé' }, { status: 404 })
    }
    
    if (userWithData._count.orders > 0 || userWithData._count.stockMovements > 0) {
      // Désactiver au lieu de supprimer
      await prisma.user.update({
        where: { id },
        data: { isActive: false }
      })
      return NextResponse.json({ message: 'Utilisateur désactivé' })
    }
    
    await prisma.user.delete({
      where: { id }
    })

    return NextResponse.json({ message: 'Utilisateur supprimé' })
  } catch (error) {
    console.error('Erreur lors de la suppression de l\'utilisateur:', error)
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 })
  }
}