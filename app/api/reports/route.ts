import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get("type");
    const format = searchParams.get("format") || "json";

    let data: unknown[] = [];

    switch (type) {
      case "inventory":
        data = await prisma.product.findMany({
          include: {
            category: true,
          },
          orderBy: { name: "asc" },
        });
        break;

      case "sales":
        const startDate = searchParams.get("startDate");
        const endDate = searchParams.get("endDate");

        const where: {
          createdAt?: { gte?: Date; lte?: Date };
        } = {};
        if (startDate) where.createdAt = { gte: new Date(startDate) };
        if (endDate)
          where.createdAt = { ...where.createdAt, lte: new Date(endDate) };

        data = await prisma.order.findMany({
          where,
          include: {
            customer: true,
            items: {
              include: {
                product: true,
              },
            },
          },
          orderBy: { createdAt: "desc" },
        });
        break;

      case "stock-movements":
        data = await prisma.stockMovement.findMany({
          include: {
            product: true,
            user: true,
            supplier: true,
          },
          orderBy: { createdAt: "desc" },
          take: 1000,
        });
        break;

      default:
        return NextResponse.json(
          { error: "Type de rapport non supporté" },
          { status: 400 },
        );
    }

    if (format === "csv") {
      const csv = generateCSV(data, type);
      return new NextResponse(csv, {
        headers: {
          "Content-Type": "text/csv",
          "Content-Disposition": `attachment; filename="${type}-${Date.now()}.csv"`,
        },
      });
    }

    return NextResponse.json({ data, type });
  } catch (error) {
    console.error("Erreur lors de la génération du rapport:", error);
    return NextResponse.json({ error: "Erreur serveur" }, { status: 500 });
  }
}

function generateCSV(data: unknown[], type: string): string {
  if (!data.length) return "";

  let headers: string[] = [];
  let rows: string[][] = [];

  switch (type) {
    case "inventory":
      headers = ["Nom", "SKU", "Catégorie", "Prix", "Stock", "Stock Min"];
      rows = data.map((item) => {
        const product = item as {
          name: string;
          sku: string;
          category?: { name: string } | null;
          price: number;
          quantity: number;
          minStock: number;
        };
        return [
          product.name,
          product.sku,
          product.category?.name || "Sans catégorie",
          product.price.toString(),
          product.quantity.toString(),
          product.minStock.toString(),
        ];
      });
      break;

    case "sales":
      headers = ["Numéro", "Client", "Montant", "Statut", "Date"];
      rows = data.map((item) => {
        const order = item as {
          orderNumber: string;
          customer?: { name: string } | null;
          customerName?: string | null;
          totalAmount: number;
          status: string;
          createdAt: string;
        };
        return [
          order.orderNumber,
          order.customer?.name || order.customerName || "Client anonyme",
          order.totalAmount.toString(),
          order.status,
          new Date(order.createdAt).toLocaleDateString("fr-FR"),
        ];
      });
      break;

    case "stock-movements":
      headers = ["Type", "Produit", "Quantité", "Utilisateur", "Date"];
      rows = data.map((item) => {
        const movement = item as {
          type: string;
          product: { name: string };
          quantity: number;
          user: { name: string };
          createdAt: string;
        };
        return [
          movement.type,
          movement.product.name,
          movement.quantity.toString(),
          movement.user.name,
          new Date(movement.createdAt).toLocaleDateString("fr-FR"),
        ];
      });
      break;
  }

  const csvContent = [
    headers.join(","),
    ...rows.map((row) => row.map((cell) => `"${cell}"`).join(",")),
  ].join("\n");

  return csvContent;
}
