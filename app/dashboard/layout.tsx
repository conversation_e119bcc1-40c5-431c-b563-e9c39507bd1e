"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Calendar,
  Bell
} from "lucide-react"
import { Toaster } from "sonner"
import { AppSidebar, SidebarTrigger } from "@/components/app-sidebar"
import { SidebarInset } from "@/components/ui/sidebar"


export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/signin")
    }
  }, [status, router])

  if (status === "loading") {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-background to-accent/20">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Chargement...</p>
        </div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  return (
    <AppSidebar>
      <SidebarInset className="bg-gradient-to-br from-background to-accent/20">
        {/* Top header */}
        <header className="bg-background/80 backdrop-blur-xl shadow-lg border-b border-border sticky top-0 z-10">
          <div className="flex items-center justify-between h-16 px-6">
            <div className="flex items-center gap-4">
              <SidebarTrigger className="hover:bg-accent/50 transition-colors" />
              <div className="hidden sm:block">
                <h1 className="text-xl font-semibold text-foreground">
                  Tableau de bord
                </h1>
                <p className="text-sm text-muted-foreground">
                  Bienvenue, {session.user.name}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              {/* Date */}
              <div className="hidden md:flex items-center gap-2 bg-gradient-to-r from-accent/50 to-secondary/50 px-4 py-2 rounded-xl">
                <Calendar className="w-4 h-4 text-primary" />
                <span className="text-sm font-medium text-foreground">
                  {new Date().toLocaleDateString('fr-FR', {
                    weekday: 'short',
                    day: 'numeric',
                    month: 'short'
                  })}
                </span>
              </div>
              
              {/* Notifications */}
              <Button variant="ghost" size="sm" className="relative hover:bg-accent/50 transition-colors">
                <Bell className="w-5 h-5 text-muted-foreground" />
                <span className="absolute top-1 right-1 w-2 h-2 bg-destructive rounded-full"></span>
              </Button>
              
              {/* Status indicator */}
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-xs text-muted-foreground hidden sm:inline">En ligne</span>
              </div>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="flex-1 overflow-y-auto p-6">
          {children}
        </main>

        {/* Toaster for notifications */}
        <Toaster />
      </SidebarInset>
    </AppSidebar>
  )
}