"use client";

import { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import {
  Plus,
  Truck,
  Search,
  Edit,
  Trash2,
  Mail,
  Phone,
  MapPin,
} from "lucide-react";
import { toast } from "sonner";
import { PermissionGuard } from "@/components/PermissionGuard";
import { PERMISSIONS } from "@/lib/permissions";

interface Supplier {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  createdAt: string;
  _count: {
    stockMovements: number;
  };
}

export default function SuppliersPage() {
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingSupplier, setEditingSupplier] = useState<Supplier | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalSuppliers, setTotalSuppliers] = useState(0);

  const [supplierForm, setSupplierForm] = useState({
    name: "",
    email: "",
    phone: "",
    address: "",
  });

  const fetchSuppliers = useCallback(
    async (page = currentPage) => {
      try {
        setLoading(true);
        const params = new URLSearchParams({
          page: page.toString(),
          limit: "12",
        });
        if (searchTerm) params.append("search", searchTerm);

        const response = await fetch(`/api/suppliers?${params}`);
        if (response.ok) {
          const data = await response.json();
          setSuppliers(data.suppliers);
          setTotalPages(data.pagination.pages);
          setTotalSuppliers(data.pagination.total);
          setCurrentPage(page);
        }
      } catch (error) {
        console.error(
          "Erreur lors de la récupération des fournisseurs:",
          error,
        );
      } finally {
        setLoading(false);
      }
    },
    [currentPage, searchTerm],
  );

  useEffect(() => {
    fetchSuppliers();
  }, [fetchSuppliers]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setCurrentPage(1);
      fetchSuppliers(1);
    }, 500);
    return () => clearTimeout(timer);
  }, [searchTerm, fetchSuppliers]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!supplierForm.name.trim()) {
      toast.error("Le nom du fournisseur est requis");
      return;
    }

    try {
      const url = editingSupplier
        ? `/api/suppliers/${editingSupplier.id}`
        : "/api/suppliers";
      const method = editingSupplier ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(supplierForm),
      });

      if (response.ok) {
        toast.success(
          editingSupplier ? "Fournisseur mis à jour" : "Fournisseur créé",
        );
        setIsDialogOpen(false);
        resetForm();
        fetchSuppliers(1);
        setCurrentPage(1);
      } else {
        const error = await response.json();
        toast.error(error.error || "Erreur lors de l'opération");
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur serveur");
    }
  };

  const handleEdit = (supplier: Supplier) => {
    setEditingSupplier(supplier);
    setSupplierForm({
      name: supplier.name,
      email: supplier.email || "",
      phone: supplier.phone || "",
      address: supplier.address || "",
    });
    setIsDialogOpen(true);
  };

  const handleDelete = async (supplier: Supplier) => {
    if (!confirm(`Êtes-vous sûr de vouloir supprimer ${supplier.name} ?`))
      return;

    try {
      const response = await fetch(`/api/suppliers/${supplier.id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        toast.success("Fournisseur supprimé");
        fetchSuppliers(currentPage);
      } else {
        toast.error("Erreur lors de la suppression");
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur serveur");
    }
  };

  const resetForm = () => {
    setSupplierForm({ name: "", email: "", phone: "", address: "" });
    setEditingSupplier(null);
  };

  return (
    <div className="space-y-8 max-w-7xl mx-auto">
      {/* Header */}
      <div className="bg-background/80 backdrop-blur-xl rounded-2xl shadow-xl border border-border p-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent flex items-center">
              <Truck className="h-10 w-10 text-primary mr-4" />
              Fournisseurs
            </h1>
            <p className="text-muted-foreground mt-3 text-lg">
              Gérez votre base de données fournisseurs
            </p>
          </div>

          <PermissionGuard permission={PERMISSIONS.SUPPLIERS_MANAGE}>
            <Dialog
              open={isDialogOpen}
              onOpenChange={(open) => {
                setIsDialogOpen(open);
                if (!open) resetForm();
              }}
            >
              <DialogTrigger asChild>
                <Button className="bg-primary hover:bg-primary/90">
                  <Plus className="h-4 w-4 mr-2" />
                  Nouveau fournisseur
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>
                    {editingSupplier
                      ? "Modifier le fournisseur"
                      : "Créer un fournisseur"}
                  </DialogTitle>
                  <DialogDescription>
                    Renseignez les informations du fournisseur
                  </DialogDescription>
                </DialogHeader>

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="space-y-2">
                    <Label>Nom du fournisseur *</Label>
                    <Input
                      value={supplierForm.name}
                      onChange={(e) =>
                        setSupplierForm({
                          ...supplierForm,
                          name: e.target.value,
                        })
                      }
                      placeholder="Nom de l'entreprise"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Email</Label>
                    <Input
                      type="email"
                      value={supplierForm.email}
                      onChange={(e) =>
                        setSupplierForm({
                          ...supplierForm,
                          email: e.target.value,
                        })
                      }
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Téléphone</Label>
                    <Input
                      value={supplierForm.phone}
                      onChange={(e) =>
                        setSupplierForm({
                          ...supplierForm,
                          phone: e.target.value,
                        })
                      }
                      placeholder="+223 XX XX XX XX"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Adresse</Label>
                    <Textarea
                      value={supplierForm.address}
                      onChange={(e) =>
                        setSupplierForm({
                          ...supplierForm,
                          address: e.target.value,
                        })
                      }
                      placeholder="Adresse complète du fournisseur"
                      rows={3}
                    />
                  </div>

                  <DialogFooter>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsDialogOpen(false)}
                    >
                      Annuler
                    </Button>
                    <Button type="submit">
                      {editingSupplier ? "Mettre à jour" : "Créer"}
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </PermissionGuard>
        </div>
      </div>

      {/* Recherche et statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-gradient-to-r from-primary to-primary/80 text-primary-foreground border-0 shadow-xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-primary-foreground/80 text-sm font-medium">
                  Total Fournisseurs
                </p>
                <p className="text-3xl font-bold">{totalSuppliers}</p>
              </div>
              <Truck className="h-10 w-10 text-primary-foreground/60" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-background/80 backdrop-blur-xl border-border shadow-lg md:col-span-3">
          <CardContent className="p-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
              <Input
                placeholder="Rechercher un fournisseur..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 h-12 text-lg"
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Liste des fournisseurs */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-6 bg-muted rounded mb-4"></div>
                <div className="h-4 bg-muted rounded w-3/4"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : suppliers.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <Truck className="h-16 w-16 mx-auto mb-4 text-muted-foreground/30" />
            <h3 className="text-lg font-semibold text-foreground mb-2">
              Aucun fournisseur
            </h3>
            <p className="text-muted-foreground mb-6">
              Commencez par ajouter votre premier fournisseur.
            </p>
            <Button
              onClick={() => setIsDialogOpen(true)}
              className="bg-primary hover:bg-primary/90"
            >
              <Plus className="h-4 w-4 mr-2" />
              Créer un fournisseur
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {suppliers.map((supplier) => (
            <Card
              key={supplier.id}
              className="bg-background/80 backdrop-blur-xl border-border shadow-lg hover:shadow-xl transition-shadow"
            >
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1">
                    <h3 className="text-xl font-bold text-foreground mb-2">
                      {supplier.name}
                    </h3>
                    <div className="space-y-2">
                      {supplier.email && (
                        <div className="flex items-center text-muted-foreground">
                          <Mail className="h-4 w-4 mr-2" />
                          <span className="text-sm">{supplier.email}</span>
                        </div>
                      )}
                      {supplier.phone && (
                        <div className="flex items-center text-muted-foreground">
                          <Phone className="h-4 w-4 mr-2" />
                          <span className="text-sm">{supplier.phone}</span>
                        </div>
                      )}
                      {supplier.address && (
                        <div className="flex items-center text-muted-foreground">
                          <MapPin className="h-4 w-4 mr-2" />
                          <span className="text-sm">{supplier.address}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="bg-muted/50 rounded-lg p-3 mb-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">
                      Mouvements de stock
                    </span>
                    <span className="text-lg font-bold text-primary">
                      {supplier._count.stockMovements}
                    </span>
                  </div>
                </div>

                <div className="flex justify-between items-center text-sm text-muted-foreground/70 mb-4">
                  <span>
                    Créé le{" "}
                    {new Date(supplier.createdAt).toLocaleDateString("fr-FR")}
                  </span>
                </div>

                <div className="flex gap-2">
                  <PermissionGuard permission={PERMISSIONS.SUPPLIERS_MANAGE}>
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={() => handleEdit(supplier)}
                    >
                      <Edit className="h-4 w-4 mr-2" />
                      Modifier
                    </Button>
                  </PermissionGuard>
                  <PermissionGuard permission={PERMISSIONS.SUPPLIERS_MANAGE}>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(supplier)}
                      className="text-destructive hover:text-destructive/80"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </PermissionGuard>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center items-center space-x-2 mt-8">
          <Button
            variant="outline"
            onClick={() => fetchSuppliers(currentPage - 1)}
            disabled={currentPage === 1 || loading}
          >
            Précédent
          </Button>

          <div className="flex space-x-1">
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              const pageNum =
                Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;
              if (pageNum > totalPages) return null;

              return (
                <Button
                  key={pageNum}
                  variant={currentPage === pageNum ? "default" : "outline"}
                  size="sm"
                  onClick={() => fetchSuppliers(pageNum)}
                  disabled={loading}
                >
                  {pageNum}
                </Button>
              );
            })}
          </div>

          <Button
            variant="outline"
            onClick={() => fetchSuppliers(currentPage + 1)}
            disabled={currentPage === totalPages || loading}
          >
            Suivant
          </Button>
        </div>
      )}
    </div>
  );
}
