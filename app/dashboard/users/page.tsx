"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Plus, Users, Edit, Trash2, Shield, UserCheck, UserX } from "lucide-react"
import { toast } from "sonner"

interface User {
  id: string
  name: string
  email: string
  role: 'ADMIN' | 'MANAGER' | 'EMPLOYEE'
  isActive: boolean
  createdAt: string
  _count: {
    orders: number
    stockMovements: number
  }
}

export default function UsersPage() {
  const { data: session } = useSession()
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  
  const [userForm, setUserForm] = useState({
    name: "",
    email: "",
    password: "",
    role: "EMPLOYEE" as 'ADMIN' | 'MANAGER' | 'EMPLOYEE',
    isActive: true
  })

  useEffect(() => {
    fetchUsers()
  }, [])

  const fetchUsers = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/users")
      if (response.ok) {
        const data = await response.json()
        setUsers(data.users)
      }
    } catch (error) {
      console.error("Erreur lors de la récupération des utilisateurs:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!userForm.name.trim() || !userForm.email.trim()) {
      toast.error("Nom et email sont requis")
      return
    }
    
    if (!editingUser && !userForm.password) {
      toast.error("Mot de passe requis pour un nouvel utilisateur")
      return
    }
    
    try {
      const url = editingUser ? `/api/users/${editingUser.id}` : "/api/users"
      const method = editingUser ? "PUT" : "POST"
      
      const body = editingUser && !userForm.password 
        ? { ...userForm, password: undefined }
        : userForm
      
      const response = await fetch(url, {
        method,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(body)
      })

      if (response.ok) {
        toast.success(editingUser ? "Utilisateur mis à jour" : "Utilisateur créé")
        setIsDialogOpen(false)
        resetForm()
        fetchUsers()
      } else {
        const error = await response.json()
        toast.error(error.error || "Erreur lors de l'opération")
      }
    } catch (error) {
      console.error("Erreur:", error)
      toast.error("Erreur serveur")
    }
  }

  const handleEdit = (user: User) => {
    setEditingUser(user)
    setUserForm({
      name: user.name,
      email: user.email,
      password: "",
      role: user.role,
      isActive: user.isActive
    })
    setIsDialogOpen(true)
  }

  const handleDelete = async (user: User) => {
    if (!confirm(`Êtes-vous sûr de vouloir supprimer ${user.name} ?`)) return
    
    try {
      const response = await fetch(`/api/users/${user.id}`, {
        method: "DELETE"
      })

      if (response.ok) {
        const result = await response.json()
        toast.success(result.message)
        fetchUsers()
      } else {
        const error = await response.json()
        toast.error(error.error || "Erreur lors de la suppression")
      }
    } catch (error) {
      console.error("Erreur:", error)
      toast.error("Erreur serveur")
    }
  }

  const toggleUserStatus = async (user: User) => {
    try {
      const response = await fetch(`/api/users/${user.id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ isActive: !user.isActive })
      })

      if (response.ok) {
        toast.success(`Utilisateur ${!user.isActive ? 'activé' : 'désactivé'}`)
        fetchUsers()
      } else {
        toast.error("Erreur lors de la mise à jour")
      }
    } catch (error) {
      console.error("Erreur:", error)
      toast.error("Erreur serveur")
    }
  }

  const resetForm = () => {
    setUserForm({ name: "", email: "", password: "", role: "EMPLOYEE", isActive: true })
    setEditingUser(null)
  }

  const getRoleText = (role: string) => {
    switch (role) {
      case 'ADMIN': return 'Administrateur'
      case 'MANAGER': return 'Manager'
      case 'EMPLOYEE': return 'Employé'
      default: return role
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'ADMIN': return 'bg-destructive/10 text-destructive'
      case 'MANAGER': return 'bg-primary/10 text-primary'
      case 'EMPLOYEE': return 'bg-secondary/10 text-secondary'
      default: return 'bg-muted/10 text-muted-foreground'
    }
  }

  const canManageUsers = session?.user?.role === 'ADMIN'

  if (!canManageUsers) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Shield className="h-16 w-16 mx-auto mb-4 text-muted-foreground/30" />
          <h3 className="text-lg font-semibold text-foreground mb-2">Accès restreint</h3>
          <p className="text-muted-foreground">Seuls les administrateurs peuvent gérer les utilisateurs.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8 max-w-7xl mx-auto">
      {/* Header */}
      <div className="bg-background/80 backdrop-blur-xl rounded-2xl shadow-xl border border-border p-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent flex items-center">
              <Users className="h-10 w-10 text-primary mr-4" />
              Utilisateurs
            </h1>
            <p className="text-muted-foreground mt-3 text-lg">Gérez les utilisateurs et leurs permissions</p>
          </div>
          
          <Dialog open={isDialogOpen} onOpenChange={(open) => {
            setIsDialogOpen(open)
            if (!open) resetForm()
          }}>
            <DialogTrigger asChild>
              <Button className="bg-primary hover:bg-primary/90">
                <Plus className="h-4 w-4 mr-2" />
                Nouvel utilisateur
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>
                  {editingUser ? "Modifier l'utilisateur" : "Créer un utilisateur"}
                </DialogTitle>
                <DialogDescription>
                  Configurez les informations et permissions de l&apos;utilisateur
                </DialogDescription>
              </DialogHeader>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-2">
                  <Label>Nom complet *</Label>
                  <Input
                    value={userForm.name}
                    onChange={(e) => setUserForm({...userForm, name: e.target.value})}
                    placeholder="Nom de l'utilisateur"
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>Email *</Label>
                  <Input
                    type="email"
                    value={userForm.email}
                    onChange={(e) => setUserForm({...userForm, email: e.target.value})}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>Mot de passe {!editingUser && '*'}</Label>
                  <Input
                    type="password"
                    value={userForm.password}
                    onChange={(e) => setUserForm({...userForm, password: e.target.value})}
                    placeholder={editingUser ? "Laisser vide pour ne pas changer" : "Minimum 6 caractères"}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>Rôle *</Label>
                  <Select value={userForm.role} onValueChange={(value: 'ADMIN' | 'MANAGER' | 'EMPLOYEE') => setUserForm({...userForm, role: value})}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="EMPLOYEE">Employé</SelectItem>
                      <SelectItem value="MANAGER">Manager</SelectItem>
                      <SelectItem value="ADMIN">Administrateur</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <Label>Compte actif</Label>
                    <p className="text-sm text-muted-foreground">L&apos;utilisateur peut se connecter</p>
                  </div>
                  <Switch
                    checked={userForm.isActive}
                    onCheckedChange={(checked) => setUserForm({...userForm, isActive: checked})}
                  />
                </div>
                
                <DialogFooter>
                  <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                    Annuler
                  </Button>
                  <Button type="submit">
                    {editingUser ? "Mettre à jour" : "Créer"}
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-gradient-to-r from-primary to-primary/80 text-primary-foreground border-0 shadow-xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-primary-foreground/80 text-sm font-medium">Total Utilisateurs</p>
                <p className="text-3xl font-bold">{users.length}</p>
              </div>
              <Users className="h-10 w-10 text-primary-foreground/60" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-secondary to-secondary/80 text-secondary-foreground border-0 shadow-xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-foreground/80 text-sm font-medium">Utilisateurs Actifs</p>
                <p className="text-3xl font-bold">{users.filter(u => u.isActive).length}</p>
              </div>
              <UserCheck className="h-10 w-10 text-secondary-foreground/60" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-destructive to-destructive/80 text-destructive-foreground border-0 shadow-xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-destructive-foreground/80 text-sm font-medium">Administrateurs</p>
                <p className="text-3xl font-bold">{users.filter(u => u.role === 'ADMIN').length}</p>
              </div>
              <Shield className="h-10 w-10 text-destructive-foreground/60" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Liste des utilisateurs */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-6 bg-muted rounded mb-4"></div>
                <div className="h-4 bg-muted rounded w-3/4"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : users.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <Users className="h-16 w-16 mx-auto mb-4 text-muted-foreground/30" />
            <h3 className="text-lg font-semibold text-foreground mb-2">Aucun utilisateur</h3>
            <p className="text-muted-foreground mb-6">Commencez par créer votre premier utilisateur.</p>
            <Button onClick={() => setIsDialogOpen(true)} className="bg-primary hover:bg-primary/90">
              <Plus className="h-4 w-4 mr-2" />
              Créer un utilisateur
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {users.map((user) => (
            <Card key={user.id} className="bg-background/80 backdrop-blur-xl border-border shadow-lg hover:shadow-xl transition-shadow">
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="text-xl font-bold text-foreground">{user.name}</h3>
                      {user.isActive ? (
                        <UserCheck className="h-5 w-5 text-secondary" />
                      ) : (
                        <UserX className="h-5 w-5 text-destructive" />
                      )}
                    </div>
                    <p className="text-muted-foreground mb-2">{user.email}</p>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRoleColor(user.role)}`}>
                      {getRoleText(user.role)}
                    </span>
                  </div>
                </div>
                
                <div className="bg-muted/50 rounded-lg p-3 mb-4 space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Commandes:</span>
                    <span className="font-medium">{user._count.orders}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Mouvements:</span>
                    <span className="font-medium">{user._count.stockMovements}</span>
                  </div>
                </div>
                
                <div className="flex justify-between items-center text-sm text-muted-foreground/70 mb-4">
                  <span>Créé le {new Date(user.createdAt).toLocaleDateString('fr-FR')}</span>
                </div>
                
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1"
                    onClick={() => handleEdit(user)}
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Modifier
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => toggleUserStatus(user)}
                    className={user.isActive ? "text-destructive hover:text-destructive/80" : "text-secondary hover:text-secondary/80"}
                  >
                    {user.isActive ? <UserX className="h-4 w-4" /> : <UserCheck className="h-4 w-4" />}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(user)}
                    className="text-destructive hover:text-destructive/80"
                    disabled={user.id === session?.user?.id}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}