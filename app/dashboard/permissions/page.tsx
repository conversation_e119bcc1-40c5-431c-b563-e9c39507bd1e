"use client"

import { useState, useEffect } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Shield, Users, Package, ShoppingCart, Activity, FileText, Settings, Save } from "lucide-react"
import { PERMISSIONS } from "@/lib/permissions"
import { RoleGuard } from "@/components/PermissionGuard"
import { toast } from "sonner"

export default function PermissionsPage() {
  const [selectedRole, setSelectedRole] = useState<string>("EMPLOYEE")
  const [editMode, setEditMode] = useState(false)
  const [rolePermissions, setRolePermissions] = useState<{[key: string]: string[]}>({})
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    fetch('/api/permissions')
      .then(r => r.j<PERSON>())
      .then(setRolePermissions)
  }, [])

  const permissionGroups = [
    {
      name: "Produits",
      icon: Package,
      permissions: [
        { key: PERMISSIONS.PRODUCTS_VIEW, name: "Voir les produits" },
        { key: PERMISSIONS.PRODUCTS_CREATE, name: "Créer des produits" },
        { key: PERMISSIONS.PRODUCTS_EDIT, name: "Modifier des produits" },
        { key: PERMISSIONS.PRODUCTS_DELETE, name: "Supprimer des produits" }
      ]
    },
    {
      name: "Commandes",
      icon: ShoppingCart,
      permissions: [
        { key: PERMISSIONS.ORDERS_VIEW, name: "Voir les commandes" },
        { key: PERMISSIONS.ORDERS_CREATE, name: "Créer des commandes" },
        { key: PERMISSIONS.ORDERS_EDIT, name: "Modifier des commandes" },
        { key: PERMISSIONS.ORDERS_DELETE, name: "Supprimer des commandes" }
      ]
    },
    {
      name: "Stock",
      icon: Activity,
      permissions: [
        { key: PERMISSIONS.STOCK_VIEW, name: "Voir le stock" },
        { key: PERMISSIONS.STOCK_MOVEMENTS, name: "Gérer les mouvements" }
      ]
    },
    {
      name: "Clients",
      icon: Users,
      permissions: [
        { key: PERMISSIONS.CUSTOMERS_VIEW, name: "Voir les clients" },
        { key: PERMISSIONS.CUSTOMERS_MANAGE, name: "Gérer les clients" }
      ]
    },
    {
      name: "Rapports",
      icon: FileText,
      permissions: [
        { key: PERMISSIONS.REPORTS_VIEW, name: "Voir les rapports" },
        { key: PERMISSIONS.REPORTS_EXPORT, name: "Exporter les rapports" }
      ]
    },
    {
      name: "Administration",
      icon: Settings,
      permissions: [
        { key: PERMISSIONS.USERS_MANAGE, name: "Gérer les utilisateurs" },
        { key: PERMISSIONS.SETTINGS_MANAGE, name: "Gérer les paramètres" },
        { key: PERMISSIONS.CATEGORIES_MANAGE, name: "Gérer les catégories" }
      ]
    }
  ]

  const hasPermission = (role: string, permission: string) => {
    const permissions = rolePermissions[role as keyof typeof rolePermissions]
    return permissions?.includes(permission) || false
  }

  const togglePermission = (role: string, permission: string) => {
    if (!editMode) return
    
    setRolePermissions(prev => {
      const currentPermissions = prev[role] || []
      const hasPermission = currentPermissions.includes(permission)
      
      return {
        ...prev,
        [role]: hasPermission 
          ? currentPermissions.filter(p => p !== permission)
          : [...currentPermissions, permission]
      }
    })
  }

  const savePermissions = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/permissions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ rolePermissions })
      })
      if (response.ok) {
        toast.success("Permissions sauvegardées")
        setEditMode(false)
      } else {
        toast.error("Erreur lors de la sauvegarde")
      }
    } catch {
      toast.error("Erreur lors de la sauvegarde")
    } finally {
      setLoading(false)
    }
  }

  const resetPermissions = async () => {
    const permissions = await fetch('/api/permissions').then(r => r.json())
    setRolePermissions(permissions)
    setEditMode(false)
  }

  return (
    <RoleGuard roles={["ADMIN"]} fallback={
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Shield className="h-16 w-16 mx-auto mb-4 text-muted-foreground/30" />
          <h3 className="text-lg font-semibold text-foreground mb-2">Accès restreint</h3>
          <p className="text-muted-foreground">Seuls les administrateurs peuvent voir les permissions.</p>
        </div>
      </div>
    }>
      <div className="space-y-8 max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-background/80 backdrop-blur-xl rounded-2xl shadow-xl border border-border p-8">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent flex items-center">
              <Shield className="h-10 w-10 text-primary mr-4" />
              Gestion des Permissions
            </h1>
            <p className="text-muted-foreground mt-3 text-lg">Visualisez les permissions par rôle utilisateur</p>
          </div>
        </div>

        {/* Sélecteur de rôle */}
        <Card className="bg-background/80 backdrop-blur-xl border-border shadow-xl">
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>Sélectionner un rôle</CardTitle>
              <div className="flex gap-2">
                {!editMode ? (
                  <Button onClick={() => setEditMode(true)} variant="outline">
                    Modifier les permissions
                  </Button>
                ) : (
                  <>
                    <Button onClick={resetPermissions} variant="outline">
                      Annuler
                    </Button>
                    <Button onClick={savePermissions} disabled={loading}>
                      <Save className="h-4 w-4 mr-2" />
                      {loading ? "Sauvegarde..." : "Sauvegarder"}
                    </Button>
                  </>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              {Object.keys(rolePermissions).map((role) => (
                <button
                  key={role}
                  onClick={() => setSelectedRole(role)}
                  className={`px-6 py-3 rounded-lg font-medium transition-all ${
                    selectedRole === role
                      ? "bg-primary text-primary-foreground shadow-lg"
                      : "bg-muted text-muted-foreground hover:bg-muted/80"
                  }`}
                >
                  {role === "ADMIN" ? "Administrateur" : 
                   role === "MANAGER" ? "Manager" : "Employé"}
                </button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Matrice des permissions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {permissionGroups.map((group) => (
            <Card key={group.name} className="bg-background/80 backdrop-blur-xl border-border shadow-xl">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <group.icon className="h-5 w-5 mr-2 text-primary" />
                  {group.name}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {group.permissions.map((permission) => (
                    <div key={permission.key} className="flex items-center justify-between">
                      <span className="text-sm text-foreground">{permission.name}</span>
                      {editMode ? (
                        <Switch
                          checked={hasPermission(selectedRole, permission.key)}
                          onCheckedChange={() => togglePermission(selectedRole, permission.key)}
                        />
                      ) : (
                        <Badge 
                          variant={hasPermission(selectedRole, permission.key) ? "default" : "secondary"}
                          className={hasPermission(selectedRole, permission.key)
                            ? "bg-secondary/10 text-secondary hover:bg-secondary/10"
                            : "bg-destructive/10 text-destructive hover:bg-destructive/10"
                          }
                        >
                          {hasPermission(selectedRole, permission.key) ? "Autorise" : "Refuse"}
                        </Badge>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Résumé des permissions */}
        <Card className="bg-background/80 backdrop-blur-xl border-border shadow-xl">
          <CardHeader>
            <CardTitle>Résumé pour {selectedRole === "ADMIN" ? "Administrateur" : 
                                   selectedRole === "MANAGER" ? "Manager" : "Employé"}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-secondary">
                  {rolePermissions[selectedRole as keyof typeof rolePermissions]?.length || 0}
                </div>
                <div className="text-sm text-muted-foreground">Permissions accordées</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-destructive">
                  {Object.values(PERMISSIONS).length - (rolePermissions[selectedRole as keyof typeof rolePermissions]?.length || 0)}
                </div>
                <div className="text-sm text-muted-foreground">Permissions refusées</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary">
                  {Math.round(((rolePermissions[selectedRole as keyof typeof rolePermissions]?.length || 0) / Object.values(PERMISSIONS).length) * 100)}%
                </div>
                <div className="text-sm text-muted-foreground">Niveau d&apos;accès</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </RoleGuard>
  )
}