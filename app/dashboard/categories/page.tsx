"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Plus, FolderTree, Edit, Trash2, Folder, Package } from "lucide-react"
import { toast } from "sonner"

interface Category {
  id: string
  name: string
  description?: string
  parentId?: string
  parent?: { name: string }
  children: Category[]
  _count: {
    products: number
  }
  createdAt: string
}

export default function CategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)
  
  const [categoryForm, setCategoryForm] = useState({
    name: "",
    description: "",
    parentId: ""
  })

  useEffect(() => {
    fetchCategories()
  }, [])

  const fetchCategories = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/categories")
      if (response.ok) {
        const data = await response.json()
        setCategories(data.categories)
      }
    } catch (error) {
      console.error("Erreur lors de la récupération des catégories:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!categoryForm.name.trim()) {
      toast.error("Le nom de la catégorie est requis")
      return
    }
    
    try {
      const url = editingCategory ? `/api/categories/${editingCategory.id}` : "/api/categories"
      const method = editingCategory ? "PUT" : "POST"
      
      const response = await fetch(url, {
        method,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...categoryForm,
          parentId: categoryForm.parentId || undefined
        })
      })

      if (response.ok) {
        toast.success(editingCategory ? "Catégorie mise à jour" : "Catégorie créée")
        setIsDialogOpen(false)
        resetForm()
        fetchCategories()
      } else {
        const error = await response.json()
        toast.error(error.error || "Erreur lors de l'opération")
      }
    } catch (error) {
      console.error("Erreur:", error)
      toast.error("Erreur serveur")
    }
  }

  const handleEdit = (category: Category) => {
    setEditingCategory(category)
    setCategoryForm({
      name: category.name,
      description: category.description || "",
      parentId: category.parentId || ""
    })
    setIsDialogOpen(true)
  }

  const handleDelete = async (category: Category) => {
    if ((category._count?.products || 0) > 0) {
      toast.error("Impossible de supprimer une catégorie contenant des produits")
      return
    }
    
    if (!confirm(`Êtes-vous sûr de vouloir supprimer "${category.name}" ?`)) return
    
    try {
      const response = await fetch(`/api/categories/${category.id}`, {
        method: "DELETE"
      })

      if (response.ok) {
        toast.success("Catégorie supprimée")
        fetchCategories()
      } else {
        const error = await response.json()
        toast.error(error.error || "Erreur lors de la suppression")
      }
    } catch (error) {
      console.error("Erreur:", error)
      toast.error("Erreur serveur")
    }
  }

  const resetForm = () => {
    setCategoryForm({ name: "", description: "", parentId: "" })
    setEditingCategory(null)
  }

  const getParentCategories = () => {
    return categories.filter(cat => !cat.parentId)
  }

  const renderCategoryTree = (category: Category, level = 0) => {
    return (
      <div key={category.id}>
        <Card className="bg-background/80 backdrop-blur-xl border-border shadow-lg hover:shadow-xl transition-shadow mb-4">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0" style={{ marginLeft: `${level * 20}px` }}>
                  <Folder className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-foreground">{category.name}</h3>
                  {category.description && (
                    <p className="text-sm text-muted-foreground mt-1">{category.description}</p>
                  )}
                  {category.parent && (
                    <p className="text-xs text-muted-foreground/70 mt-1">
                      Sous-catégorie de: {category.parent.name}
                    </p>
                  )}
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className="text-right">
                  <div className="flex items-center text-muted-foreground">
                    <Package className="h-4 w-4 mr-1" />
                    <span className="text-sm font-medium">{category._count?.products || 0} produits</span>
                  </div>
                  <p className="text-xs text-muted-foreground/70 mt-1">
                    Créée le {new Date(category.createdAt).toLocaleDateString('fr-FR')}
                  </p>
                </div>
                
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEdit(category)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(category)}
                    className="text-destructive hover:text-destructive/80"
                    disabled={(category._count?.products || 0) > 0}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Sous-catégories */}
        {category.children?.map(child => renderCategoryTree(child, level + 1))}
      </div>
    )
  }

  const totalCategories = categories.length
  const totalProducts = categories.reduce((sum, cat) => sum + (cat._count?.products || 0), 0)

  return (
    <div className="space-y-8 max-w-7xl mx-auto">
      {/* Header */}
      <div className="bg-background/80 backdrop-blur-xl rounded-2xl shadow-xl border border-border p-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent flex items-center">
              <FolderTree className="h-10 w-10 text-primary mr-4" />
              Catégories
            </h1>
            <p className="text-muted-foreground mt-3 text-lg">Organisez vos produits par catégories</p>
          </div>
          
          <Dialog open={isDialogOpen} onOpenChange={(open) => {
            setIsDialogOpen(open)
            if (!open) resetForm()
          }}>
            <DialogTrigger asChild>
              <Button className="bg-primary hover:bg-primary/90">
                <Plus className="h-4 w-4 mr-2" />
                Nouvelle catégorie
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>
                  {editingCategory ? "Modifier la catégorie" : "Créer une catégorie"}
                </DialogTitle>
                <DialogDescription>
                  Organisez vos produits en catégories et sous-catégories
                </DialogDescription>
              </DialogHeader>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-2">
                  <Label>Nom de la catégorie *</Label>
                  <Input
                    value={categoryForm.name}
                    onChange={(e) => setCategoryForm({...categoryForm, name: e.target.value})}
                    placeholder="Ex: Électronique, Vêtements..."
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>Description</Label>
                  <Textarea
                    value={categoryForm.description}
                    onChange={(e) => setCategoryForm({...categoryForm, description: e.target.value})}
                    placeholder="Description de la catégorie"
                    rows={3}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>Catégorie parent</Label>
                  <Select 
                    value={categoryForm.parentId} 
                    onValueChange={(value) => setCategoryForm({...categoryForm, parentId: value === "none" ? "" : value})}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Aucune (catégorie principale)" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">Aucune (catégorie principale)</SelectItem>
                      {getParentCategories()
                        .filter(cat => cat.id !== editingCategory?.id)
                        .map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <DialogFooter>
                  <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                    Annuler
                  </Button>
                  <Button type="submit">
                    {editingCategory ? "Mettre à jour" : "Créer"}
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-gradient-to-r from-primary to-primary/80 text-primary-foreground border-0 shadow-xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-primary-foreground/80 text-sm font-medium">Total Catégories</p>
                <p className="text-3xl font-bold">{totalCategories}</p>
              </div>
              <FolderTree className="h-10 w-10 text-primary-foreground/60" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-secondary to-secondary/80 text-secondary-foreground border-0 shadow-xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-foreground/80 text-sm font-medium">Produits Organisés</p>
                <p className="text-3xl font-bold">{totalProducts}</p>
              </div>
              <Package className="h-10 w-10 text-secondary-foreground/60" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-accent to-accent/80 text-accent-foreground border-0 shadow-xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-accent-foreground/80 text-sm font-medium">Catégories Principales</p>
                <p className="text-3xl font-bold">{getParentCategories().length}</p>
              </div>
              <Folder className="h-10 w-10 text-accent-foreground/60" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Liste des catégories */}
      {loading ? (
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-6 bg-muted rounded mb-4"></div>
                <div className="h-4 bg-muted rounded w-3/4"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : categories.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <FolderTree className="h-16 w-16 mx-auto mb-4 text-muted-foreground/30" />
            <h3 className="text-lg font-semibold text-foreground mb-2">Aucune catégorie</h3>
            <p className="text-muted-foreground mb-6">Commencez par créer votre première catégorie.</p>
            <Button onClick={() => setIsDialogOpen(true)} className="bg-primary hover:bg-primary/90">
              <Plus className="h-4 w-4 mr-2" />
              Créer une catégorie
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div>
          {getParentCategories().map(category => renderCategoryTree(category))}
        </div>
      )}
    </div>
  )
}