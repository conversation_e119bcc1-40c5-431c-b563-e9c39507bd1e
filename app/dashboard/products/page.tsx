"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Plus, Search, Package } from "lucide-react";
import { toast } from "sonner";
import { PermissionGuard } from "@/components/PermissionGuard";
import { PERMISSIONS } from "@/lib/permissions";

interface Category {
  id: string;
  name: string;
}

interface Product {
  id: string;
  name: string;
  description?: string;
  sku: string;
  barcode?: string;
  price: number;
  cost: number;
  quantity: number;
  minStock: number;
  categoryId?: string;
  category?: Category;
  createdAt: string;
  updatedAt: string;
}

interface ProductFormData {
  name: string;
  description?: string;
  sku: string;
  barcode?: string;
  price: number;
  cost: number;
  quantity: number;
  minStock: number;
  categoryId?: string;
}

export default function ProductsPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("");
  const [stockFilter, setStockFilter] = useState("");
  const [priceRange, setPriceRange] = useState({ min: "", max: "" });
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [formData, setFormData] = useState<ProductFormData>({
    name: "",
    description: "",
    sku: "",
    barcode: "",
    price: 0,
    cost: 0,
    quantity: 0,
    minStock: 0,
    categoryId: "",
  });

  useEffect(() => {
    fetchProducts();
    fetchCategories();
  }, []);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/products");
      if (response.ok) {
        const data = await response.json();
        setProducts(data.products);
      }
    } catch (error) {
      console.error("Erreur lors de la récupération des produits:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await fetch("/api/categories");
      if (response.ok) {
        const data = await response.json();
        setCategories(data.categories || []);
      }
    } catch (error) {
      console.error("Erreur lors de la récupération des catégories:", error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      const url = editingProduct
        ? `/api/products/${editingProduct.id}`
        : "/api/products";
      const method = editingProduct ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        toast.success(editingProduct ? "Produit mis à jour" : "Produit créé");
        setIsDialogOpen(false);
        resetForm();
        await fetchProducts();
      } else {
        const error = await response.json();
        toast.error(error.error || "Erreur lors de l'opération");
      }
    } catch (error) {
      console.error("Erreur lors de la sauvegarde du produit:", error);
      toast.error("Erreur serveur");
    } finally {
      setSubmitting(false);
    }
  };

  const handleEdit = (product: Product) => {
    setEditingProduct(product);
    setFormData({
      name: product.name,
      description: product.description || "",
      sku: product.sku,
      barcode: product.barcode || "",
      price: product.price,
      cost: product.cost,
      quantity: product.quantity,
      minStock: product.minStock,
      categoryId: product.categoryId || "",
    });
    setIsDialogOpen(true);
  };

  const handleDelete = async (id: string) => {
    if (confirm("Êtes-vous sûr de vouloir supprimer ce produit ?")) {
      try {
        const response = await fetch(`/api/products/${id}`, {
          method: "DELETE",
        });

        if (response.ok) {
          toast.success("Produit supprimé");
          fetchProducts();
        } else {
          toast.error("Erreur lors de la suppression");
        }
      } catch (error) {
        console.error("Erreur lors de la suppression du produit:", error);
        toast.error("Erreur serveur");
      }
    }
  };

  const resetForm = () => {
    setEditingProduct(null);
    setFormData({
      name: "",
      description: "",
      sku: "",
      barcode: "",
      price: 0,
      cost: 0,
      quantity: 0,
      minStock: 0,
      categoryId: "",
    });
  };

  const filteredProducts = products.filter((product) => {
    // Filtre par recherche
    const matchesSearch =
      !searchTerm ||
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.sku.toLowerCase().includes(searchTerm.toLowerCase());

    // Filtre par catégorie
    const matchesCategory =
      !categoryFilter || product.categoryId === categoryFilter;

    // Filtre par stock
    const matchesStock =
      !stockFilter ||
      (stockFilter === "low" && product.quantity <= product.minStock) ||
      (stockFilter === "out" && product.quantity === 0) ||
      (stockFilter === "available" && product.quantity > 0);

    // Filtre par prix
    const matchesPrice =
      (!priceRange.min || product.price >= parseFloat(priceRange.min)) &&
      (!priceRange.max || product.price <= parseFloat(priceRange.max));

    return matchesSearch && matchesCategory && matchesStock && matchesPrice;
  });

  return (
    <div className="space-y-8 max-w-7xl mx-auto">
      {/* Header */}
      <div className="bg-background/80 backdrop-blur-xl rounded-2xl shadow-xl border border-border p-8 mb-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent flex items-center">
              <Package className="h-10 w-10 text-primary mr-4" />
              Produits
            </h1>
            <p className="text-muted-foreground mt-3 text-lg">
              Gérez votre catalogue de produits et inventaire
            </p>
          </div>

          <PermissionGuard permission={PERMISSIONS.PRODUCTS_CREATE}>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-primary hover:bg-primary/90">
                  <Plus className="h-4 w-4 mr-2" />
                  Nouveau produit
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle className="text-xl font-bold text-foreground">
                    {editingProduct
                      ? "Modifier le produit"
                      : "Créer un produit"}
                  </DialogTitle>
                  <DialogDescription className="text-muted-foreground">
                    {editingProduct
                      ? "Modifiez les informations du produit"
                      : "Ajoutez un nouveau produit à votre catalogue"}
                  </DialogDescription>
                </DialogHeader>

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label
                        htmlFor="name"
                        className="text-sm font-medium text-foreground"
                      >
                        Nom *
                      </Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) =>
                          setFormData({ ...formData, name: e.target.value })
                        }
                        className="h-10"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label
                        htmlFor="sku"
                        className="text-sm font-medium text-foreground"
                      >
                        SKU *
                      </Label>
                      <Input
                        id="sku"
                        value={formData.sku}
                        onChange={(e) =>
                          setFormData({ ...formData, sku: e.target.value })
                        }
                        className="h-10"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label
                      htmlFor="description"
                      className="text-sm font-medium text-foreground"
                    >
                      Description
                    </Label>
                    <Input
                      id="description"
                      value={formData.description}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          description: e.target.value,
                        })
                      }
                      className="h-10"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label
                        htmlFor="price"
                        className="text-sm font-medium text-foreground"
                      >
                        Prix de vente (FCFA) *
                      </Label>
                      <Input
                        id="price"
                        type="number"
                        step="0.01"
                        value={formData.price || ""}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            price: parseFloat(e.target.value) || 0,
                          })
                        }
                        className="h-10"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label
                        htmlFor="cost"
                        className="text-sm font-medium text-foreground"
                      >
                        Coût (FCFA) *
                      </Label>
                      <Input
                        id="cost"
                        type="number"
                        step="0.01"
                        value={formData.cost || ""}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            cost: parseFloat(e.target.value) || 0,
                          })
                        }
                        className="h-10"
                        required
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label
                        htmlFor="quantity"
                        className="text-sm font-medium text-foreground"
                      >
                        Quantité *
                      </Label>
                      <Input
                        id="quantity"
                        type="number"
                        value={formData.quantity || ""}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            quantity: parseInt(e.target.value) || 0,
                          })
                        }
                        className="h-10"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label
                        htmlFor="minStock"
                        className="text-sm font-medium text-foreground"
                      >
                        Stock minimum *
                      </Label>
                      <Input
                        id="minStock"
                        type="number"
                        value={formData.minStock || ""}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            minStock: parseInt(e.target.value) || 0,
                          })
                        }
                        className="h-10"
                        required
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label
                        htmlFor="barcode"
                        className="text-sm font-medium text-foreground"
                      >
                        Code barres
                      </Label>
                      <Input
                        id="barcode"
                        value={formData.barcode}
                        onChange={(e) =>
                          setFormData({ ...formData, barcode: e.target.value })
                        }
                        className="h-10"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label
                        htmlFor="category"
                        className="text-sm font-medium text-foreground"
                      >
                        Catégorie
                      </Label>
                      <Select
                        value={formData.categoryId || ""}
                        onValueChange={(value) =>
                          setFormData({
                            ...formData,
                            categoryId: value === "none" ? "" : value,
                          })
                        }
                      >
                        <SelectTrigger className="h-10">
                          <SelectValue placeholder="Sélectionner une catégorie" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">Aucune catégorie</SelectItem>
                          {categories.map((category) => (
                            <SelectItem key={category.id} value={category.id}>
                              {category.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <DialogFooter className="flex space-x-2 pt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsDialogOpen(false)}
                      className="px-4 py-2"
                    >
                      Annuler
                    </Button>
                    <Button
                      type="submit"
                      className="bg-primary hover:bg-primary/90 px-4 py-2"
                      disabled={submitting}
                    >
                      {submitting
                        ? "En cours..."
                        : editingProduct
                        ? "Mettre à jour"
                        : "Créer"}
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </PermissionGuard>
        </div>
      </div>

      {/* Filtres */}
      <Card className="bg-background/80 backdrop-blur-xl border-border shadow-xl">
        <CardContent className="pt-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Recherche */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Rechercher..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 h-10"
              />
            </div>

            {/* Filtre par catégorie */}
            <Select
              value={categoryFilter}
              onValueChange={(value) =>
                setCategoryFilter(value === "all" ? "" : value)
              }
            >
              <SelectTrigger className="h-10">
                <SelectValue placeholder="Toutes les catégories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Toutes les catégories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Filtre par stock */}
            <Select
              value={stockFilter}
              onValueChange={(value) =>
                setStockFilter(value === "all" ? "" : value)
              }
            >
              <SelectTrigger className="h-10">
                <SelectValue placeholder="Tous les stocks" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tous les stocks</SelectItem>
                <SelectItem value="available">En stock</SelectItem>
                <SelectItem value="low">Stock faible</SelectItem>
                <SelectItem value="out">Rupture</SelectItem>
              </SelectContent>
            </Select>

            {/* Filtre par prix */}
            <div className="flex gap-2">
              <Input
                type="number"
                placeholder="Prix min"
                value={priceRange.min}
                onChange={(e) =>
                  setPriceRange({ ...priceRange, min: e.target.value })
                }
                className="h-10"
              />
              <Input
                type="number"
                placeholder="Prix max"
                value={priceRange.max}
                onChange={(e) =>
                  setPriceRange({ ...priceRange, max: e.target.value })
                }
                className="h-10"
              />
            </div>
          </div>

          {/* Bouton reset */}
          <div className="mt-4 flex justify-end">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setSearchTerm("");
                setCategoryFilter("");
                setStockFilter("");
                setPriceRange({ min: "", max: "" });
              }}
            >
              Réinitialiser
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Résultats */}
      <div className="flex justify-between items-center">
        <p className="text-sm text-muted-foreground">
          {filteredProducts.length} produit
          {filteredProducts.length > 1 ? "s" : ""} trouvé
          {filteredProducts.length > 1 ? "s" : ""}
        </p>
      </div>

      {/* Products Grid */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse border-border">
              <CardHeader className="pb-3">
                <div className="h-6 bg-muted rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="h-4 bg-muted rounded"></div>
                  <div className="h-4 bg-muted rounded w-3/4"></div>
                  <div className="h-4 bg-muted rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : filteredProducts.length === 0 ? (
        <Card className="border-border">
          <CardContent className="text-center py-12">
            <Package className="h-16 w-16 mx-auto mb-4 text-muted-foreground/30" />
            <h3 className="text-lg font-semibold text-foreground mb-2">
              {searchTerm ? "Aucun produit trouvé" : "Aucun produit"}
            </h3>
            <p className="text-muted-foreground mb-6">
              {searchTerm
                ? "Aucun produit ne correspond à votre recherche."
                : "Commencez par ajouter votre premier produit à votre catalogue."}
            </p>
            {!searchTerm && (
              <Button
                onClick={() => setIsDialogOpen(true)}
                className="bg-primary hover:bg-primary/90"
              >
                <Plus className="h-4 w-4 mr-2" />
                Créer un produit
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProducts.map((product) => (
            <Card
              key={product.id}
              className="border-border hover:shadow-lg hover:border-primary/50 transition-all duration-200"
            >
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <CardTitle className="text-lg font-semibold text-foreground leading-tight">
                      {product.name}
                    </CardTitle>
                    <CardDescription className="text-sm text-muted-foreground mt-1">
                      SKU: {product.sku}
                    </CardDescription>
                  </div>
                  <div className="flex space-x-1 ml-2">
                    <PermissionGuard permission={PERMISSIONS.PRODUCTS_EDIT}>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEdit(product)}
                        className="text-primary hover:text-primary/80 hover:bg-primary/10 h-8 w-8 p-0"
                      >
                        <svg
                          className="h-4 w-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                          />
                        </svg>
                      </Button>
                    </PermissionGuard>
                    <PermissionGuard permission={PERMISSIONS.PRODUCTS_DELETE}>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(product.id)}
                        className="text-destructive hover:text-destructive/80 hover:bg-destructive/10 h-8 w-8 p-0"
                      >
                        <svg
                          className="h-4 w-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                          />
                        </svg>
                      </Button>
                    </PermissionGuard>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Prix:</span>
                    <span className="font-semibold text-foreground">
                      {product.price.toFixed(2)} FCFA
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">
                      Stock:
                    </span>
                    <span
                      className={`font-semibold text-sm px-2 py-1 rounded-full ${
                        product.quantity <= product.minStock
                          ? "bg-destructive/10 text-destructive"
                          : "bg-secondary/10 text-secondary"
                      }`}
                    >
                      {product.quantity}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">
                      Min. stock:
                    </span>
                    <span className="font-medium text-foreground">
                      {product.minStock}
                    </span>
                  </div>
                  {product.category && (
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">
                        Catégorie:
                      </span>
                      <span className="font-medium text-foreground text-sm">
                        {product.category.name}
                      </span>
                    </div>
                  )}
                </div>
                {product.description && (
                  <div className="mt-4 pt-3 border-t border-border">
                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {product.description}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
