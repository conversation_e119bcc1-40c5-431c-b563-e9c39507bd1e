"use client";

import { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import {
  Plus,
  TrendingUp,
  TrendingDown,
  RotateCcw,
  Package,
} from "lucide-react";
import { toast } from "sonner";
import { PermissionGuard } from "@/components/PermissionGuard";
import { PERMISSIONS } from "@/lib/permissions";

interface Product {
  id: string;
  name: string;
  sku: string;
  quantity: number;
}

interface Supplier {
  id: string;
  name: string;
}

interface StockMovement {
  id: string;
  type: "IN" | "OUT" | "ADJUSTMENT";
  quantity: number;
  notes?: string;
  createdAt: string;
  product: Product;
  supplier?: Supplier;
  user: { name: string };
  order?: { orderNumber: string };
}

export default function StockPage() {
  const [movements, setMovements] = useState<StockMovement[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [typeFilter, setTypeFilter] = useState("");
  const [productFilter, setProductFilter] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const [movementForm, setMovementForm] = useState({
    type: "" as "IN" | "OUT" | "ADJUSTMENT" | "",
    quantity: 0,
    productId: "",
    supplierId: "",
    notes: "",
  });

  const fetchMovements = useCallback(
    async (page = currentPage) => {
      try {
        setLoading(true);
        const params = new URLSearchParams({
          page: page.toString(),
          limit: "20",
        });
        if (typeFilter && typeFilter !== "all")
          params.append("type", typeFilter);
        if (productFilter && productFilter !== "all")
          params.append("productId", productFilter);

        const response = await fetch(`/api/stock-movements?${params}`);
        if (response.ok) {
          const data = await response.json();
          setMovements(data.movements);
          setTotalPages(data.pagination.pages);
          setCurrentPage(page);
        }
      } catch (error) {
        console.error("Erreur lors de la récupération des mouvements:", error);
      } finally {
        setLoading(false);
      }
    },
    [currentPage, typeFilter, productFilter],
  );

  useEffect(() => {
    fetchMovements();
    fetchProducts();
    fetchSuppliers();
  }, [fetchMovements]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setCurrentPage(1);
      fetchMovements(1);
    }, 500);
    return () => clearTimeout(timer);
  }, [typeFilter, productFilter, fetchMovements]);

  const fetchProducts = async () => {
    try {
      const response = await fetch("/api/products");
      if (response.ok) {
        const data = await response.json();
        setProducts(data.products);
      }
    } catch (error) {
      console.error("Erreur lors de la récupération des produits:", error);
    }
  };

  const fetchSuppliers = async () => {
    try {
      const response = await fetch("/api/suppliers");
      if (response.ok) {
        const data = await response.json();
        setSuppliers(data.suppliers || []);
      }
    } catch (error) {
      console.error("Erreur lors de la récupération des fournisseurs:", error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (
      !movementForm.type ||
      !movementForm.productId ||
      movementForm.quantity <= 0
    ) {
      toast.error("Veuillez remplir tous les champs obligatoires");
      return;
    }

    try {
      const response = await fetch("/api/stock-movements", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...movementForm,
          supplierId:
            movementForm.supplierId === "none"
              ? undefined
              : movementForm.supplierId,
        }),
      });

      if (response.ok) {
        toast.success("Mouvement de stock enregistré");
        setIsDialogOpen(false);
        resetForm();
        fetchMovements(1);
        fetchProducts(); // Rafraîchir les quantités
        setCurrentPage(1);
      } else {
        const error = await response.json();
        toast.error(error.error || "Erreur lors de l'enregistrement");
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur serveur");
    }
  };

  const resetForm = () => {
    setMovementForm({
      type: "",
      quantity: 0,
      productId: "",
      supplierId: "",
      notes: "",
    });
  };

  const getMovementIcon = (type: string) => {
    switch (type) {
      case "IN":
        return <TrendingUp className="h-5 w-5 text-secondary" />;
      case "OUT":
        return <TrendingDown className="h-5 w-5 text-destructive" />;
      case "ADJUSTMENT":
        return <RotateCcw className="h-5 w-5 text-primary" />;
      default:
        return <Package className="h-5 w-5 text-muted-foreground" />;
    }
  };

  const getMovementColor = (type: string) => {
    switch (type) {
      case "IN":
        return "bg-secondary/10 text-secondary";
      case "OUT":
        return "bg-destructive/10 text-destructive";
      case "ADJUSTMENT":
        return "bg-primary/10 text-primary";
      default:
        return "bg-muted/10 text-muted-foreground";
    }
  };

  const getMovementText = (type: string) => {
    switch (type) {
      case "IN":
        return "Entrée";
      case "OUT":
        return "Sortie";
      case "ADJUSTMENT":
        return "Ajustement";
      default:
        return type;
    }
  };

  const getMovementDescription = (movement: StockMovement) => {
    if (movement.order) {
      return `Vente - ${movement.order.orderNumber}`;
    }
    if (movement.supplier) {
      return `Réception - ${movement.supplier.name}`;
    }
    return movement.notes || "Mouvement manuel";
  };

  return (
    <div className="space-y-8 max-w-7xl mx-auto">
      {/* Header */}
      <div className="bg-background/80 backdrop-blur-xl rounded-2xl shadow-xl border border-border p-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent flex items-center">
              <Package className="h-10 w-10 text-primary mr-4" />
              Mouvements de Stock
            </h1>
            <p className="text-muted-foreground mt-3 text-lg">
              Gérez les entrées, sorties et ajustements de stock
            </p>
          </div>

          <PermissionGuard permission={PERMISSIONS.STOCK_MOVEMENTS}>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-primary hover:bg-primary/90">
                  <Plus className="h-4 w-4 mr-2" />
                  Nouveau mouvement
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>Créer un mouvement de stock</DialogTitle>
                  <DialogDescription>
                    Enregistrez une entrée, sortie ou ajustement de stock
                  </DialogDescription>
                </DialogHeader>

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Type de mouvement *</Label>
                      <Select
                        value={movementForm.type}
                        onValueChange={(value: "IN" | "OUT" | "ADJUSTMENT") =>
                          setMovementForm({ ...movementForm, type: value })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionner" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="IN">Entrée de stock</SelectItem>
                          <SelectItem value="OUT">Sortie de stock</SelectItem>
                          <SelectItem value="ADJUSTMENT">Ajustement</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Quantité *</Label>
                      <Input
                        type="number"
                        min="1"
                        value={movementForm.quantity || ""}
                        onChange={(e) =>
                          setMovementForm({
                            ...movementForm,
                            quantity: parseInt(e.target.value) || 0,
                          })
                        }
                        placeholder="0"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Produit *</Label>
                    <Select
                      value={movementForm.productId}
                      onValueChange={(value) =>
                        setMovementForm({ ...movementForm, productId: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Sélectionner un produit" />
                      </SelectTrigger>
                      <SelectContent>
                        {products.map((product) => (
                          <SelectItem key={product.id} value={product.id}>
                            {product.name} - Stock: {product.quantity}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {movementForm.type === "IN" && (
                    <div className="space-y-2">
                      <Label>Fournisseur</Label>
                      <Select
                        value={movementForm.supplierId}
                        onValueChange={(value) =>
                          setMovementForm({
                            ...movementForm,
                            supplierId: value,
                          })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionner un fournisseur" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">
                            Aucun fournisseur
                          </SelectItem>
                          {suppliers.map((supplier) => (
                            <SelectItem key={supplier.id} value={supplier.id}>
                              {supplier.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  <div className="space-y-2">
                    <Label>Notes</Label>
                    <Textarea
                      value={movementForm.notes}
                      onChange={(e) =>
                        setMovementForm({
                          ...movementForm,
                          notes: e.target.value,
                        })
                      }
                      placeholder="Commentaires optionnels..."
                      rows={3}
                    />
                  </div>

                  <DialogFooter>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsDialogOpen(false)}
                    >
                      Annuler
                    </Button>
                    <Button type="submit">Enregistrer</Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </PermissionGuard>
        </div>
      </div>

      {/* Filtres */}
      <Card className="bg-background/80 backdrop-blur-xl border-border shadow-xl">
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>Filtrer par type</Label>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Tous les types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tous les types</SelectItem>
                  <SelectItem value="IN">Entrées</SelectItem>
                  <SelectItem value="OUT">Sorties</SelectItem>
                  <SelectItem value="ADJUSTMENT">Ajustements</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Filtrer par produit</Label>
              <Select value={productFilter} onValueChange={setProductFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Tous les produits" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tous les produits</SelectItem>
                  {products.map((product) => (
                    <SelectItem key={product.id} value={product.id}>
                      {product.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-end">
              <Button
                variant="outline"
                onClick={() => {
                  setTypeFilter("");
                  setProductFilter("");
                }}
                className="w-full"
              >
                Réinitialiser
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Liste des mouvements */}
      {loading ? (
        <div className="grid gap-4">
          {[...Array(5)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-6 bg-muted rounded mb-4"></div>
                <div className="h-4 bg-muted rounded w-3/4"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : movements.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <Package className="h-16 w-16 mx-auto mb-4 text-muted-foreground/30" />
            <h3 className="text-lg font-semibold text-foreground mb-2">
              Aucun mouvement
            </h3>
            <p className="text-muted-foreground mb-6">
              Commencez par enregistrer votre premier mouvement de stock.
            </p>
            <Button
              onClick={() => setIsDialogOpen(true)}
              className="bg-primary hover:bg-primary/90"
            >
              <Plus className="h-4 w-4 mr-2" />
              Créer un mouvement
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {movements.map((movement) => (
            <Card
              key={movement.id}
              className="bg-background/80 backdrop-blur-xl border-border shadow-lg hover:shadow-xl transition-shadow"
            >
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      {getMovementIcon(movement.type)}
                    </div>
                    <div>
                      <div className="flex items-center space-x-3">
                        <h3 className="text-lg font-semibold text-foreground">
                          {movement.product.name}
                        </h3>
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-medium ${getMovementColor(movement.type)}`}
                        >
                          {getMovementText(movement.type)}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">
                        SKU: {movement.product.sku} •{" "}
                        {getMovementDescription(movement)}
                      </p>
                      <p className="text-xs text-muted-foreground/70 mt-1">
                        {new Date(movement.createdAt).toLocaleDateString(
                          "fr-FR",
                          {
                            day: "2-digit",
                            month: "2-digit",
                            year: "numeric",
                            hour: "2-digit",
                            minute: "2-digit",
                          },
                        )}{" "}
                        • Par {movement.user.name}
                      </p>
                    </div>
                  </div>

                  <div className="text-right">
                    <p
                      className={`text-2xl font-bold ${
                        movement.type === "IN"
                          ? "text-secondary"
                          : movement.type === "OUT"
                            ? "text-destructive"
                            : "text-primary"
                      }`}
                    >
                      {movement.type === "OUT"
                        ? "-"
                        : movement.type === "IN"
                          ? "+"
                          : ""}
                      {movement.quantity}
                    </p>
                    <p className="text-sm text-muted-foreground/70">unités</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center items-center space-x-2 mt-8">
              <Button
                variant="outline"
                onClick={() => fetchMovements(currentPage - 1)}
                disabled={currentPage === 1 || loading}
              >
                Précédent
              </Button>

              <div className="flex space-x-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const pageNum =
                    Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;
                  if (pageNum > totalPages) return null;

                  return (
                    <Button
                      key={pageNum}
                      variant={currentPage === pageNum ? "default" : "outline"}
                      size="sm"
                      onClick={() => fetchMovements(pageNum)}
                      disabled={loading}
                    >
                      {pageNum}
                    </Button>
                  );
                })}
              </div>

              <Button
                variant="outline"
                onClick={() => fetchMovements(currentPage + 1)}
                disabled={currentPage === totalPages || loading}
              >
                Suivant
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
