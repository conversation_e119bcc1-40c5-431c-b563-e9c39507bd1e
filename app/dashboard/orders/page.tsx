"use client";

import { useState, useEffect, useCallback } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Plus, ShoppingCart, Trash2 } from "lucide-react";
import { toast } from "sonner";
import { PermissionGuard } from "@/components/PermissionGuard";
import { PERMISSIONS } from "@/lib/permissions";

interface Customer {
  id: string;
  name: string;
  email?: string;
  phone?: string;
}

interface Product {
  id: string;
  name: string;
  price: number;
  quantity: number;
}

interface OrderItem {
  productId: string;
  quantity: number;
  price: number;
  product?: Product;
}

interface Order {
  id: string;
  orderNumber: string;
  status: string;
  totalAmount: number;
  customer: Customer;
  customerName?: string;
  items: OrderItem[];
  createdAt: string;
}

export default function OrdersPage() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalOrders, setTotalOrders] = useState(0);
  const itemsPerPage = 12;

  const [orderForm, setOrderForm] = useState({
    customerId: "",
    customerName: "",
    items: [] as OrderItem[],
  });

  const [clientType, setClientType] = useState<"registered" | "anonymous">(
    "anonymous",
  );

  const [newItem, setNewItem] = useState({
    productId: "",
    quantity: 1,
  });

  const fetchOrders = useCallback(
    async (page = currentPage) => {
      try {
        setLoading(true);
        const params = new URLSearchParams({
          page: page.toString(),
          limit: itemsPerPage.toString(),
        });
        if (statusFilter) params.append("status", statusFilter);
        if (searchTerm) params.append("search", searchTerm);

        const response = await fetch(`/api/orders?${params}`);
        if (response.ok) {
          const data = await response.json();
          setOrders(data.orders);
          setTotalPages(data.pagination.pages);
          setTotalOrders(data.pagination.total);
          setCurrentPage(page);
        }
      } catch (error) {
        console.error("Erreur lors de la récupération des commandes:", error);
      } finally {
        setLoading(false);
      }
    },
    [currentPage, statusFilter, searchTerm, itemsPerPage],
  );

  useEffect(() => {
    fetchOrders();
    fetchCustomers();
    fetchProducts();
  }, [fetchOrders]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setCurrentPage(1);
      fetchOrders(1);
    }, 500);
    return () => clearTimeout(timer);
  }, [searchTerm, statusFilter, fetchOrders]);

  const fetchCustomers = async () => {
    try {
      const response = await fetch("/api/customers");
      if (response.ok) {
        const data = await response.json();
        setCustomers(data.customers);
      }
    } catch (error) {
      console.error("Erreur lors de la récupération des clients:", error);
    }
  };

  const fetchProducts = async () => {
    try {
      const response = await fetch("/api/products");
      if (response.ok) {
        const data = await response.json();
        setProducts(data.products);
      }
    } catch (error) {
      console.error("Erreur lors de la récupération des produits:", error);
    }
  };

  const addItemToOrder = () => {
    if (!newItem.productId) return;

    const product = products.find((p) => p.id === newItem.productId);
    if (!product) return;

    // Vérifier le stock disponible
    const currentQuantityInOrder = orderForm.items
      .filter((item) => item.productId === newItem.productId)
      .reduce((sum, item) => sum + item.quantity, 0);

    if (currentQuantityInOrder + newItem.quantity > product.quantity) {
      toast.error(
        `Stock insuffisant. Disponible: ${
          product.quantity - currentQuantityInOrder
        }`,
      );
      return;
    }

    const existingItemIndex = orderForm.items.findIndex(
      (item) => item.productId === newItem.productId,
    );

    if (existingItemIndex >= 0) {
      const updatedItems = [...orderForm.items];
      updatedItems[existingItemIndex].quantity += newItem.quantity;
      setOrderForm({ ...orderForm, items: updatedItems });
    } else {
      setOrderForm({
        ...orderForm,
        items: [
          ...orderForm.items,
          {
            productId: newItem.productId,
            quantity: newItem.quantity,
            price: product.price,
            product,
          },
        ],
      });
    }

    setNewItem({ productId: "", quantity: 1 });
  };

  const removeItemFromOrder = (productId: string) => {
    setOrderForm({
      ...orderForm,
      items: orderForm.items.filter((item) => item.productId !== productId),
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (orderForm.items.length === 0) {
      toast.error("Veuillez ajouter au moins un produit");
      return;
    }

    if (clientType === "registered" && !orderForm.customerId) {
      toast.error("Veuillez sélectionner un client");
      return;
    }

    try {
      const response = await fetch("/api/orders", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(orderForm),
      });

      if (response.ok) {
        toast.success("Commande créée");
        setIsDialogOpen(false);
        resetForm();
        fetchOrders(1);
        setCurrentPage(1);
      } else {
        const error = await response.json();
        toast.error(error.error || "Erreur lors de la création");
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur serveur");
    }
  };

  const resetForm = () => {
    setOrderForm({ customerId: "", customerName: "", items: [] });
    setNewItem({ productId: "", quantity: 1 });
    setClientType("anonymous");
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "PENDING":
        return "bg-yellow-100 text-yellow-800";
      case "CONFIRMED":
        return "bg-blue-100 text-blue-800";
      case "SHIPPED":
        return "bg-purple-100 text-purple-800";
      case "DELIVERED":
        return "bg-green-100 text-green-800";
      case "CANCELLED":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "PENDING":
        return "En attente";
      case "CONFIRMED":
        return "Confirmée";
      case "SHIPPED":
        return "Expédiée";
      case "DELIVERED":
        return "Livrée";
      case "CANCELLED":
        return "Annulée";
      default:
        return status;
    }
  };

  const totalAmount = orderForm.items.reduce(
    (sum, item) => sum + item.quantity * item.price,
    0,
  );

  const updateOrderStatus = async (orderId: string, newStatus: string) => {
    try {
      const response = await fetch(`/api/orders/${orderId}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        toast.success("Statut mis à jour");
        fetchOrders(currentPage);
      } else {
        const error = await response.json();
        toast.error(error.error || "Erreur lors de la mise à jour");
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur serveur");
    }
  };

  return (
    <div className="space-y-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="bg-background rounded-xl shadow-sm border border-border p-6 mb-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-foreground flex items-center">
              <ShoppingCart className="h-8 w-8 text-primary mr-3" />
              Commandes
            </h1>
            <p className="text-muted-foreground mt-2">
              Gérez vos commandes clients et ventes
            </p>
          </div>

          <PermissionGuard permission={PERMISSIONS.ORDERS_CREATE}>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-primary hover:bg-primary/90">
                  <Plus className="h-4 w-4 mr-2" />
                  Nouvelle commande
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Créer une commande</DialogTitle>
                  <DialogDescription>
                    Sélectionnez un client et ajoutez des produits
                  </DialogDescription>
                </DialogHeader>

                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Type de client */}
                  <div className="space-y-4">
                    <Label className="text-base font-semibold">
                      Type de client
                    </Label>
                    <div className="grid grid-cols-2 gap-4">
                      <div
                        className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                          clientType === "anonymous"
                            ? "border-blue-500 bg-blue-50"
                            : "border-gray-200 hover:border-gray-300"
                        }`}
                        onClick={() => setClientType("anonymous")}
                      >
                        <div className="flex items-center space-x-2">
                          <div
                            className={`w-4 h-4 rounded-full border-2 ${
                              clientType === "anonymous"
                                ? "border-blue-500 bg-blue-500"
                                : "border-gray-300"
                            }`}
                          >
                            {clientType === "anonymous" && (
                              <div className="w-2 h-2 bg-white rounded-full m-0.5"></div>
                            )}
                          </div>
                          <span className="font-medium">Client anonyme</span>
                        </div>
                        <p className="text-sm text-gray-600 mt-2">
                          Vente directe sans enregistrement
                        </p>
                      </div>

                      <div
                        className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                          clientType === "registered"
                            ? "border-blue-500 bg-blue-50"
                            : "border-gray-200 hover:border-gray-300"
                        }`}
                        onClick={() => setClientType("registered")}
                      >
                        <div className="flex items-center space-x-2">
                          <div
                            className={`w-4 h-4 rounded-full border-2 ${
                              clientType === "registered"
                                ? "border-blue-500 bg-blue-500"
                                : "border-gray-300"
                            }`}
                          >
                            {clientType === "registered" && (
                              <div className="w-2 h-2 bg-white rounded-full m-0.5"></div>
                            )}
                          </div>
                          <span className="font-medium">Client enregistré</span>
                        </div>
                        <p className="text-sm text-gray-600 mt-2">
                          Sélectionner un client existant
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Informations client */}
                  {clientType === "anonymous" ? (
                    <div className="space-y-2">
                      <Label>Nom du client (optionnel)</Label>
                      <Input
                        value={orderForm.customerName}
                        onChange={(e) =>
                          setOrderForm({
                            ...orderForm,
                            customerName: e.target.value,
                          })
                        }
                        placeholder="Nom du client..."
                      />
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <Label>Client *</Label>
                      <Select
                        value={orderForm.customerId}
                        onValueChange={(value) =>
                          setOrderForm({ ...orderForm, customerId: value })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionner un client" />
                        </SelectTrigger>
                        <SelectContent>
                          {customers.map((customer) => (
                            <SelectItem key={customer.id} value={customer.id}>
                              {customer.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  {/* Ajouter produit */}
                  <div className="border rounded-lg p-4 space-y-4">
                    <h3 className="font-medium">Ajouter un produit</h3>
                    <div className="grid grid-cols-3 gap-4">
                      <Select
                        value={newItem.productId}
                        onValueChange={(value) =>
                          setNewItem({ ...newItem, productId: value })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Produit" />
                        </SelectTrigger>
                        <SelectContent>
                          {products.map((product) => (
                            <SelectItem key={product.id} value={product.id}>
                              {product.name} - {product.price}FCFA
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <Input
                        type="number"
                        min="1"
                        value={newItem.quantity}
                        onChange={(e) =>
                          setNewItem({
                            ...newItem,
                            quantity: parseInt(e.target.value) || 1,
                          })
                        }
                        placeholder="Quantité"
                      />
                      <Button type="button" onClick={addItemToOrder}>
                        Ajouter
                      </Button>
                    </div>
                  </div>

                  {/* Liste des produits */}
                  {orderForm.items.length > 0 && (
                    <div className="border rounded-lg p-4">
                      <h3 className="font-medium mb-4">Produits commandés</h3>
                      <div className="space-y-2">
                        {orderForm.items.map((item) => (
                          <div
                            key={item.productId}
                            className="flex justify-between items-center p-2 bg-gray-50 rounded"
                          >
                            <div>
                              <span className="font-medium">
                                {item.product?.name}
                              </span>
                              <span className="text-gray-600 ml-2">
                                x{item.quantity}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <span>
                                {(item.quantity * item.price).toFixed(2)}FCFA
                              </span>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() =>
                                  removeItemFromOrder(item.productId)
                                }
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        ))}
                        <div className="border-t pt-2 font-bold text-right">
                          Total: {totalAmount.toFixed(2)}FCFA
                        </div>
                      </div>
                    </div>
                  )}

                  <DialogFooter>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsDialogOpen(false)}
                    >
                      Annuler
                    </Button>
                    <Button type="submit">Créer la commande</Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </PermissionGuard>
        </div>
      </div>

      {/* Filtres et statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <Card className="bg-gradient-to-r from-primary to-primary/80 text-primary-foreground">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-primary-foreground/80">Total commandes</p>
                <p className="text-2xl font-bold">{totalOrders}</p>
              </div>
              <ShoppingCart className="h-8 w-8 text-primary-foreground/60" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-secondary to-secondary/80 text-secondary-foreground">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-foreground/80">Page actuelle</p>
                <p className="text-2xl font-bold">
                  {currentPage}/{totalPages}
                </p>
              </div>
              <div className="h-8 w-8 bg-secondary-foreground/20 rounded-full flex items-center justify-center">
                <span className="text-secondary-foreground font-bold">
                  {orders.length}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-accent to-accent/80 text-accent-foreground">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-accent-foreground/80">En attente</p>
                <p className="text-2xl font-bold">
                  {orders.filter((o) => o.status === "PENDING").length}
                </p>
              </div>
              <div className="h-8 w-8 bg-accent-foreground/20 rounded-full flex items-center justify-center">
                <span className="text-accent-foreground font-bold">!</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-background border border-border md:col-span-2">
          <CardContent className="p-6 space-y-4">
            <div>
              <Label className="text-sm font-medium text-foreground mb-2 block">
                Rechercher
              </Label>
              <Input
                placeholder="Rechercher par numéro ou client..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div>
              <Label className="text-sm font-medium text-foreground mb-2 block">
                Filtrer par statut
              </Label>
              <Select
                value={statusFilter}
                onValueChange={(value) => {
                  setStatusFilter(value === "all" ? "" : value);
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Tous les statuts" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tous les statuts</SelectItem>
                  <SelectItem value="PENDING">En attente</SelectItem>
                  <SelectItem value="CONFIRMED">Confirmée</SelectItem>
                  <SelectItem value="SHIPPED">Expédiée</SelectItem>
                  <SelectItem value="DELIVERED">Livrée</SelectItem>
                  <SelectItem value="CANCELLED">Annulée</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Liste des commandes */}
      {loading ? (
        <div className="grid gap-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-6 bg-muted rounded mb-4"></div>
                <div className="h-4 bg-muted rounded w-3/4"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : orders.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <ShoppingCart className="h-16 w-16 mx-auto mb-4 text-muted-foreground/30" />
            <h3 className="text-lg font-semibold text-foreground mb-2">
              Aucune commande
            </h3>
            <p className="text-muted-foreground mb-6">
              Commencez par créer votre première commande.
            </p>
            <Button
              onClick={() => setIsDialogOpen(true)}
              className="bg-primary hover:bg-primary/90"
            >
              <Plus className="h-4 w-4 mr-2" />
              Créer une commande
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {orders.map((order) => (
            <Card
              key={order.id}
              className="hover:shadow-xl transition-all duration-300 border-0 shadow-md"
            >
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1">
                    <h3 className="text-lg font-bold text-foreground mb-1">
                      {order.orderNumber}
                    </h3>
                    <p className="text-muted-foreground flex items-center">
                      <span className="w-2 h-2 bg-primary rounded-full mr-2"></span>
                      {order.customer
                        ? order.customer.name
                        : order.customerName || "Client anonyme"}
                    </p>
                  </div>
                  <span
                    className={`px-3 py-1 rounded-full text-xs font-semibold ${getStatusColor(
                      order.status,
                    )}`}
                  >
                    {getStatusText(order.status)}
                  </span>
                </div>

                <div className="bg-muted/50 rounded-lg p-4 mb-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">
                      Montant total
                    </span>
                    <span className="text-xl font-bold text-foreground">
                      {order.totalAmount.toFixed(2)} FCFA
                    </span>
                  </div>
                </div>

                <div className="flex justify-between items-center text-sm">
                  <span className="text-muted-foreground">
                    {order.items.length} article
                    {order.items.length > 1 ? "s" : ""}
                  </span>
                  <span className="text-muted-foreground/70">
                    {new Date(order.createdAt).toLocaleDateString("fr-FR", {
                      day: "2-digit",
                      month: "2-digit",
                      year: "numeric",
                    })}
                  </span>
                </div>

                <div className="mt-4 pt-4 border-t border-border flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1"
                    onClick={() =>
                      (window.location.href = `/dashboard/orders/${order.id}`)
                    }
                  >
                    Voir détails
                  </Button>
                  <PermissionGuard permission={PERMISSIONS.ORDERS_EDIT}>
                    {order.status === "PENDING" && (
                      <Button
                        size="sm"
                        onClick={() => updateOrderStatus(order.id, "CONFIRMED")}
                        className="bg-primary hover:bg-primary/90"
                      >
                        Confirmer
                      </Button>
                    )}
                    {order.status === "CONFIRMED" && (
                      <Button
                        size="sm"
                        onClick={() => updateOrderStatus(order.id, "DELIVERED")}
                        className="bg-secondary hover:bg-secondary/90"
                      >
                        Livrer
                      </Button>
                    )}
                  </PermissionGuard>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center items-center space-x-2 mt-8">
          <Button
            variant="outline"
            onClick={() => fetchOrders(currentPage - 1)}
            disabled={currentPage === 1 || loading}
          >
            Précédent
          </Button>

          <div className="flex space-x-1">
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              const pageNum =
                Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;
              if (pageNum > totalPages) return null;

              return (
                <Button
                  key={pageNum}
                  variant={currentPage === pageNum ? "default" : "outline"}
                  size="sm"
                  onClick={() => fetchOrders(pageNum)}
                  disabled={loading}
                >
                  {pageNum}
                </Button>
              );
            })}
          </div>

          <Button
            variant="outline"
            onClick={() => fetchOrders(currentPage + 1)}
            disabled={currentPage === totalPages || loading}
          >
            Suivant
          </Button>
        </div>
      )}
    </div>
  );
}
