"use client";

import { useState, useEffect, useCallback } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ArrowLeft, Printer, Trash2 } from "lucide-react";
import { toast } from "sonner";

interface OrderDetails {
  id: string;
  orderNumber: string;
  status: string;
  totalAmount: number;
  customerName?: string;
  customer?: { name: string; email?: string; phone?: string };
  items: Array<{
    id: string;
    quantity: number;
    price: number;
    product: { name: string; sku: string };
  }>;
  createdAt: string;
}

export default function OrderDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const [order, setOrder] = useState<OrderDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);

  const fetchOrder = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/orders/${params.id}`);
      if (response.ok) {
        const data = await response.json();
        setOrder(data);
      } else {
        toast.error("Commande non trouvée");
        router.push("/dashboard/orders");
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur lors du chargement");
    } finally {
      setLoading(false);
    }
  }, [params.id, router]);

  useEffect(() => {
    fetchOrder();
  }, [params.id, fetchOrder]);

  const updateStatus = async (newStatus: string) => {
    try {
      setUpdating(true);
      const response = await fetch(`/api/orders/${params.id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        toast.success("Statut mis à jour");
        fetchOrder();
      } else {
        const error = await response.json();
        toast.error(error.error || "Erreur lors de la mise à jour");
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur serveur");
    } finally {
      setUpdating(false);
    }
  };

  const deleteOrder = async () => {
    if (!confirm("Êtes-vous sûr de vouloir supprimer cette commande ?")) return;

    try {
      const response = await fetch(`/api/orders/${params.id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        toast.success("Commande supprimée");
        router.push("/dashboard/orders");
      } else {
        toast.error("Erreur lors de la suppression");
      }
    } catch (error) {
      console.error("Erreur:", error);
      toast.error("Erreur serveur");
    }
  };

  const printOrder = () => {
    window.open(`/api/orders/${params.id}/print`, "_blank");
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "PENDING":
        return "bg-accent/10 text-accent";
      case "CONFIRMED":
        return "bg-primary/10 text-primary";
      case "SHIPPED":
        return "bg-purple-500/10 text-purple-500";
      case "DELIVERED":
        return "bg-secondary/10 text-secondary";
      case "CANCELLED":
        return "bg-destructive/10 text-destructive";
      default:
        return "bg-muted/10 text-muted-foreground";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "PENDING":
        return "En attente";
      case "CONFIRMED":
        return "Confirmée";
      case "SHIPPED":
        return "Expédiée";
      case "DELIVERED":
        return "Livrée";
      case "CANCELLED":
        return "Annulée";
      default:
        return status;
    }
  };

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded mb-4"></div>
          <div className="h-64 bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  if (!order) {
    return <div>Commande non trouvée</div>;
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{order.orderNumber}</h1>
            <p className="text-muted-foreground">
              {order.customer
                ? order.customer.name
                : order.customerName || "Client anonyme"}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={printOrder}>
            <Printer className="h-4 w-4 mr-2" />
            Imprimer
          </Button>
          <Button
            variant="outline"
            onClick={deleteOrder}
            className="text-destructive hover:text-destructive/80"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Supprimer
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Informations principales */}
        <div className="lg:col-span-2 space-y-6">
          {/* Produits */}
          <Card>
            <CardHeader>
              <CardTitle>Produits commandés</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {order.items.map((item) => (
                  <div
                    key={item.id}
                    className="flex justify-between items-center p-4 bg-muted/50 rounded-lg"
                  >
                    <div>
                      <h4 className="font-medium">{item.product.name}</h4>
                      <p className="text-sm text-muted-foreground">
                        SKU: {item.product.sku}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Quantité: {item.quantity}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">
                        {item.price.toFixed(2)} FCFA
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Total: {(item.quantity * item.price).toFixed(2)} FCFA
                      </p>
                    </div>
                  </div>
                ))}

                <div className="border-t pt-4">
                  <div className="flex justify-between items-center text-lg font-bold">
                    <span>Total général</span>
                    <span>{order.totalAmount.toFixed(2)} FCFA</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Statut */}
          <Card>
            <CardHeader>
              <CardTitle>Statut de la commande</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span>Statut actuel:</span>
                <span
                  className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}
                >
                  {getStatusText(order.status)}
                </span>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Changer le statut:
                </label>
                <Select
                  value={order.status}
                  onValueChange={updateStatus}
                  disabled={updating}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PENDING">En attente</SelectItem>
                    <SelectItem value="CONFIRMED">Confirmée</SelectItem>
                    <SelectItem value="SHIPPED">Expédiée</SelectItem>
                    <SelectItem value="DELIVERED">Livrée</SelectItem>
                    <SelectItem value="CANCELLED">Annulée</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Informations client */}
          {order.customer && (
            <Card>
              <CardHeader>
                <CardTitle>Informations client</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div>
                  <span className="font-medium">Nom:</span>
                  <p>{order.customer.name}</p>
                </div>
                {order.customer.email && (
                  <div>
                    <span className="font-medium">Email:</span>
                    <p>{order.customer.email}</p>
                  </div>
                )}
                {order.customer.phone && (
                  <div>
                    <span className="font-medium">Téléphone:</span>
                    <p>{order.customer.phone}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Informations commande */}
          <Card>
            <CardHeader>
              <CardTitle>Détails</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div>
                <span className="font-medium">Date de création:</span>
                <p>
                  {new Date(order.createdAt).toLocaleDateString("fr-FR", {
                    day: "2-digit",
                    month: "2-digit",
                    year: "numeric",
                    hour: "2-digit",
                    minute: "2-digit",
                  })}
                </p>
              </div>
              <div>
                <span className="font-medium">Nombre d&apos;articles:</span>
                <p>
                  {order.items.reduce((sum, item) => sum + item.quantity, 0)}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
