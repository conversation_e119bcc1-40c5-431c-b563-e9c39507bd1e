"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Settings, Building, Bell, Database, Save } from "lucide-react";
import { toast } from "sonner";
import { useSession } from "next-auth/react";

export default function SettingsPage() {
  const { data: session } = useSession();
  const [loading, setLoading] = useState(false);

  const [companySettings, setCompanySettings] = useState({
    name: "StocksPP",
    address: "",
    phone: "",
    email: "",
    currency: "FCFA",
    taxRate: 0,
  });


  const handleSaveCompany = async () => {
    setLoading(true);
    // Simulation de sauvegarde
    setTimeout(() => {
      toast.success("Paramètres de l'entreprise sauvegardés");
      setLoading(false);
    }, 1000);
  };


  const handleBackup = async () => {
    setLoading(true);
    try {
      // Simulation de sauvegarde
      setTimeout(() => {
        toast.success("Sauvegarde créée avec succès");
        setLoading(false);
      }, 2000);
    } catch {
      toast.error("Erreur lors de la sauvegarde");
      setLoading(false);
    }
  };

  return (
    <div className="space-y-8 max-w-7xl mx-auto">
      {/* Header */}
      <div className="bg-background/80 backdrop-blur-xl rounded-2xl shadow-xl border border-border p-8">
        <div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent flex items-center">
            <Settings className="h-10 w-10 text-primary mr-4" />
            Paramètres
          </h1>
          <p className="text-muted-foreground mt-3 text-lg">
            Configuration de votre application
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Paramètres de l'entreprise */}
        <Card className="bg-background/80 backdrop-blur-xl border-border shadow-xl">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Building className="h-5 w-5 mr-2 text-primary" />
              Informations de l&apos;entreprise
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Nom de l&apos;entreprise</Label>
              <Input
                value={companySettings.name}
                onChange={(e) =>
                  setCompanySettings({
                    ...companySettings,
                    name: e.target.value,
                  })
                }
                placeholder="Nom de votre entreprise"
              />
            </div>

            <div className="space-y-2">
              <Label>Adresse</Label>
              <Textarea
                value={companySettings.address}
                onChange={(e) =>
                  setCompanySettings({
                    ...companySettings,
                    address: e.target.value,
                  })
                }
                placeholder="Adresse complète"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Téléphone</Label>
                <Input
                  value={companySettings.phone}
                  onChange={(e) =>
                    setCompanySettings({
                      ...companySettings,
                      phone: e.target.value,
                    })
                  }
                  placeholder="+223 XX XX XX XX"
                />
              </div>

              <div className="space-y-2">
                <Label>Email</Label>
                <Input
                  type="email"
                  value={companySettings.email}
                  onChange={(e) =>
                    setCompanySettings({
                      ...companySettings,
                      email: e.target.value,
                    })
                  }
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Devise</Label>
                <Select
                  value={companySettings.currency}
                  onValueChange={(value) =>
                    setCompanySettings({ ...companySettings, currency: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="FCFA">FCFA</SelectItem>
                    <SelectItem value="EUR">Euro (FCFA)</SelectItem>
                    <SelectItem value="USD">Dollar ($)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Taux de TVA (%)</Label>
                <Input
                  type="number"
                  min="0"
                  max="100"
                  value={companySettings.taxRate}
                  onChange={(e) =>
                    setCompanySettings({
                      ...companySettings,
                      taxRate: parseFloat(e.target.value) || 0,
                    })
                  }
                  placeholder="18"
                />
              </div>
            </div>

            <Button
              onClick={handleSaveCompany}
              disabled={loading}
              className="w-full"
            >
              <Save className="h-4 w-4 mr-2" />
              Sauvegarder
            </Button>
          </CardContent>
        </Card>


        {/* Sauvegarde et sécurité */}
        <Card className="bg-background/80 backdrop-blur-xl border-border shadow-xl">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Database className="h-5 w-5 mr-2 text-accent" />
              Sauvegarde et sécurité
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 bg-primary/10 rounded-lg">
              <h4 className="font-medium text-primary mb-2">
                Sauvegarde manuelle
              </h4>
              <p className="text-sm text-primary/80 mb-3">
                Créez une sauvegarde complète de vos données
              </p>
              <Button
                onClick={handleBackup}
                disabled={loading}
                variant="outline"
              >
                <Database className="h-4 w-4 mr-2" />
                {loading ? "Sauvegarde..." : "Créer une sauvegarde"}
              </Button>
            </div>

            <div className="p-4 bg-secondary/10 rounded-lg">
              <h4 className="font-medium text-secondary mb-2">
                Dernière sauvegarde
              </h4>
              <p className="text-sm text-secondary/80">
                {new Date().toLocaleDateString("fr-FR")} à{" "}
                {new Date().toLocaleTimeString("fr-FR")}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Informations utilisateur */}
        <Card className="bg-background/80 backdrop-blur-xl border-border shadow-xl">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Bell className="h-5 w-5 mr-2 text-foreground" />
              Informations utilisateur
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Nom d&apos;utilisateur</Label>
              <Input value={session?.user?.name || ""} disabled />
            </div>

            <div className="space-y-2">
              <Label>Email</Label>
              <Input value={session?.user?.email || ""} disabled />
            </div>

            <div className="space-y-2">
              <Label>Rôle</Label>
              <Input value={session?.user?.role || ""} disabled />
            </div>

            <div className="p-4 bg-muted/50 rounded-lg">
              <h4 className="font-medium text-foreground mb-2">
                Version de l&apos;application
              </h4>
              <p className="text-sm text-muted-foreground">StocksPP v1.0.0</p>
              <p className="text-xs text-muted-foreground/70 mt-1">
                Dernière mise à jour: {new Date().toLocaleDateString("fr-FR")}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
