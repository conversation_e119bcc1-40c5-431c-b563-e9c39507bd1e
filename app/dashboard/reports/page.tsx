"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { FileText, Download, Calendar, Package, ShoppingCart, Activity } from "lucide-react"
import { toast } from "sonner"

export default function ReportsPage() {
  const [loading, setLoading] = useState(false)
  const [reportForm, setReportForm] = useState({
    type: "",
    format: "csv",
    startDate: "",
    endDate: ""
  })

  const handleExport = async () => {
    if (!reportForm.type) {
      toast.error("Veuillez sélectionner un type de rapport")
      return
    }

    try {
      setLoading(true)
      const params = new URLSearchParams({
        type: reportForm.type,
        format: reportForm.format
      })
      
      if (reportForm.startDate) params.append('startDate', reportForm.startDate)
      if (reportForm.endDate) params.append('endDate', reportForm.endDate)
      
      const response = await fetch(`/api/reports?${params}`)
      
      if (response.ok) {
        if (reportForm.format === 'csv') {
          const blob = await response.blob()
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = `${reportForm.type}-${Date.now()}.csv`
          document.body.appendChild(a)
          a.click()
          window.URL.revokeObjectURL(url)
          document.body.removeChild(a)
          toast.success("Rapport exporté avec succès")
        } else {
          const data = await response.json()
          console.log('Données du rapport:', data)
          toast.success("Rapport généré avec succès")
        }
      } else {
        toast.error("Erreur lors de la génération du rapport")
      }
    } catch (error) {
      console.error("Erreur:", error)
      toast.error("Erreur serveur")
    } finally {
      setLoading(false)
    }
  }

  const reportTypes = [
    {
      id: 'inventory',
      name: 'Inventaire',
      description: 'Liste complète des produits avec stocks',
      icon: Package,
      color: 'from-blue-500 to-blue-600'
    },
    {
      id: 'sales',
      name: 'Ventes',
      description: 'Rapport des commandes et ventes',
      icon: ShoppingCart,
      color: 'from-green-500 to-green-600'
    },
    {
      id: 'stock-movements',
      name: 'Mouvements de Stock',
      description: 'Historique des mouvements de stock',
      icon: Activity,
      color: 'from-purple-500 to-purple-600'
    }
  ]

  return (
    <div className="space-y-8 max-w-7xl mx-auto">
      {/* Header */}
      <div className="bg-background/80 backdrop-blur-xl rounded-2xl shadow-xl border border-border p-8">
        <div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent flex items-center">
            <FileText className="h-10 w-10 text-primary mr-4" />
            Rapports et Exports
          </h1>
          <p className="text-muted-foreground mt-3 text-lg">Générez et exportez vos rapports d&apos;activité</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Types de rapports */}
        <div className="lg:col-span-2 space-y-6">
          <Card className="bg-background/80 backdrop-blur-xl border-border shadow-xl">
            <CardHeader>
              <CardTitle>Types de rapports disponibles</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {reportTypes.map((type) => (
                <div
                  key={type.id}
                  className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                    reportForm.type === type.id
                      ? "border-primary bg-primary/10"
                      : "border-border hover:border-muted-foreground/30"
                  }`}
                  onClick={() => setReportForm({...reportForm, type: type.id})}
                >
                  <div className="flex items-center space-x-4">
                    <div className={`p-3 rounded-lg bg-gradient-to-r ${type.color}`}>
                      <type.icon className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-foreground">{type.name}</h3>
                      <p className="text-sm text-muted-foreground">{type.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Configuration */}
        <div className="space-y-6">
          <Card className="bg-background/80 backdrop-blur-xl border-border shadow-xl">
            <CardHeader>
              <CardTitle>Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Format d&apos;export</Label>
                <Select value={reportForm.format} onValueChange={(value) => setReportForm({...reportForm, format: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="csv">CSV (Excel)</SelectItem>
                    <SelectItem value="json">JSON</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {reportForm.type === 'sales' && (
                <>
                  <div className="space-y-2">
                    <Label>Date de début</Label>
                    <Input
                      type="date"
                      value={reportForm.startDate}
                      onChange={(e) => setReportForm({...reportForm, startDate: e.target.value})}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Date de fin</Label>
                    <Input
                      type="date"
                      value={reportForm.endDate}
                      onChange={(e) => setReportForm({...reportForm, endDate: e.target.value})}
                    />
                  </div>
                </>
              )}

              <Button 
                onClick={handleExport} 
                disabled={loading || !reportForm.type}
                className="w-full"
              >
                <Download className="h-4 w-4 mr-2" />
                {loading ? "Génération..." : "Générer le rapport"}
              </Button>
            </CardContent>
          </Card>

          {/* Raccourcis rapides */}
          <Card className="bg-background/80 backdrop-blur-xl border-border shadow-xl">
            <CardHeader>
              <CardTitle>Raccourcis rapides</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => {
                  setReportForm({...reportForm, type: 'inventory', format: 'csv'})
                  setTimeout(handleExport, 100)
                }}
              >
                <Package className="h-4 w-4 mr-2" />
                Export inventaire
              </Button>
              
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => {
                  const today = new Date().toISOString().split('T')[0]
                  setReportForm({
                    type: 'sales',
                    format: 'csv',
                    startDate: today,
                    endDate: today
                  })
                  setTimeout(handleExport, 100)
                }}
              >
                <Calendar className="h-4 w-4 mr-2" />
                Ventes du jour
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}