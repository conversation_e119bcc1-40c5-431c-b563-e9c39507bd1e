"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { 
  Package, 
  ShoppingCart, 
  AlertTriangle, 
  TrendingUp,
  Users,
  DollarSign,
  Activity,
  Eye,
  Calendar
} from "lucide-react"
import Link from "next/link"
import DashboardCharts from "@/components/charts/DashboardCharts"

interface DashboardStats {
  totalProducts: number
  totalOrders: number
  totalCustomers: number
  pendingOrders: number
  lowStockProducts: number
  todayOrders: number
  monthlyRevenue: number
  todayRevenue: number
}

export default function Dashboard() {
  const { data: session } = useSession()
  const [stats, setStats] = useState<DashboardStats>({
    totalProducts: 0,
    totalOrders: 0,
    totalCustomers: 0,
    pendingOrders: 0,
    lowStockProducts: 0,
    todayOrders: 0,
    monthlyRevenue: 0,
    todayRevenue: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchStats()
  }, [])

  const fetchStats = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/dashboard/stats')
      if (response.ok) {
        const data = await response.json()
        setStats(data)
      }
    } catch (error) {
      console.error('Erreur lors du chargement des statistiques:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="space-y-8 max-w-7xl mx-auto">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8 max-w-7xl mx-auto">
      {/* Header */}
      <div className="bg-background/80 backdrop-blur-xl rounded-2xl shadow-xl border border-border p-8">
        <div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent flex items-center">
            <Activity className="h-10 w-10 text-primary mr-4" />
            Tableau de bord
          </h1>
          <p className="text-muted-foreground mt-3 text-lg">
            Bienvenue, {session?.user?.name || 'Utilisateur'} ! Voici un aperçu de votre activité commerciale.
          </p>
        </div>
      </div>

      {/* Statistiques principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="bg-gradient-to-r from-primary to-primary/80 text-primary-foreground border-0 shadow-xl hover:shadow-2xl transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-primary-foreground/80 text-sm font-medium">Total Produits</p>
                <p className="text-3xl font-bold">{stats.totalProducts}</p>
              </div>
              <Package className="h-10 w-10 text-primary-foreground/60" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-secondary to-secondary/80 text-secondary-foreground border-0 shadow-xl hover:shadow-2xl transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-foreground/80 text-sm font-medium">Total Commandes</p>
                <p className="text-3xl font-bold">{stats.totalOrders}</p>
              </div>
              <ShoppingCart className="h-10 w-10 text-secondary-foreground/60" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-accent to-accent/80 text-accent-foreground border-0 shadow-xl hover:shadow-2xl transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-accent-foreground/80 text-sm font-medium">Total Clients</p>
                <p className="text-3xl font-bold">{stats.totalCustomers}</p>
              </div>
              <Users className="h-10 w-10 text-accent-foreground/60" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-muted to-muted/80 text-muted-foreground border-0 shadow-xl hover:shadow-2xl transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-muted-foreground/80 text-sm font-medium">Revenus Mois</p>
                <p className="text-3xl font-bold">{stats.monthlyRevenue.toFixed(0)} FCFA</p>
              </div>
              <DollarSign className="h-10 w-10 text-muted-foreground/60" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Statistiques secondaires */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="bg-background/80 backdrop-blur-xl border border-border shadow-lg hover:shadow-xl transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-muted-foreground text-sm font-medium">Commandes Aujourd&apos;hui</p>
                <p className="text-2xl font-bold text-foreground">{stats.todayOrders}</p>
              </div>
              <Calendar className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-background/80 backdrop-blur-xl border border-border shadow-lg hover:shadow-xl transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-muted-foreground text-sm font-medium">Revenus Aujourd&apos;hui</p>
                <p className="text-2xl font-bold text-foreground">{stats.todayRevenue.toFixed(0)} FCFA</p>
              </div>
              <DollarSign className="h-8 w-8 text-secondary" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-background/80 backdrop-blur-xl border border-border shadow-lg hover:shadow-xl transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-muted-foreground text-sm font-medium">En Attente</p>
                <p className="text-2xl font-bold text-foreground">{stats.pendingOrders}</p>
              </div>
              <Activity className="h-8 w-8 text-accent" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-background/80 backdrop-blur-xl border border-border shadow-lg hover:shadow-xl transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-muted-foreground text-sm font-medium">Stock Faible</p>
                <p className="text-2xl font-bold text-foreground">{stats.lowStockProducts}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-destructive" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Graphiques */}
      <DashboardCharts />

      {/* Actions rapides */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <Card className="bg-background/80 backdrop-blur-xl border border-border shadow-xl">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-foreground">Actions rapides</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Link href="/dashboard/orders">
              <Button className="w-full justify-start bg-primary hover:bg-primary/90">
                <ShoppingCart className="h-4 w-4 mr-2" />
                Nouvelle commande
              </Button>
            </Link>
            <Link href="/dashboard/products">
              <Button variant="outline" className="w-full justify-start">
                <Package className="h-4 w-4 mr-2" />
                Ajouter un produit
              </Button>
            </Link>
            <Link href="/dashboard/stock">
              <Button variant="outline" className="w-full justify-start">
                <TrendingUp className="h-4 w-4 mr-2" />
                Mouvement de stock
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card className="bg-background/80 backdrop-blur-xl border border-border shadow-xl">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-foreground">Alertes</CardTitle>
          </CardHeader>
          <CardContent>
            {stats.lowStockProducts > 0 ? (
              <div className="p-4 bg-destructive/10 rounded-lg border border-destructive/20">
                <div className="flex items-center">
                  <AlertTriangle className="h-5 w-5 text-destructive mr-2" />
                  <div>
                    <p className="font-medium text-destructive">{stats.lowStockProducts} produits en stock faible</p>
                    <p className="text-sm text-destructive/70">Vérifiez vos stocks</p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="p-4 bg-secondary/10 rounded-lg border border-secondary/20">
                <div className="flex items-center">
                  <div className="w-5 h-5 bg-secondary rounded-full mr-2"></div>
                  <div>
                    <p className="font-medium text-secondary">Tous les stocks sont OK</p>
                    <p className="text-sm text-secondary/70">Aucune alerte active</p>
                  </div>
                </div>
              </div>
            )}
            
            {stats.pendingOrders > 0 && (
              <div className="p-4 bg-accent/10 rounded-lg border border-accent/20 mt-4">
                <div className="flex items-center">
                  <Activity className="h-5 w-5 text-accent mr-2" />
                  <div>
                    <p className="font-medium text-accent">{stats.pendingOrders} commandes en attente</p>
                    <p className="text-sm text-accent/70">À traiter rapidement</p>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="bg-background/80 backdrop-blur-xl border border-border shadow-xl">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-foreground">Raccourcis</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Link href="/dashboard/reports">
              <Button variant="outline" className="w-full justify-start">
                <Eye className="h-4 w-4 mr-2" />
                Voir les rapports
              </Button>
            </Link>
            <Link href="/dashboard/customers">
              <Button variant="outline" className="w-full justify-start">
                <Users className="h-4 w-4 mr-2" />
                Gérer les clients
              </Button>
            </Link>
            <Link href="/dashboard/settings">
              <Button variant="outline" className="w-full justify-start">
                <Activity className="h-4 w-4 mr-2" />
                Paramètres
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}