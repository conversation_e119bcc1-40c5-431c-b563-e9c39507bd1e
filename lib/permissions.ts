export const PERMISSIONS = {
  // Produits
  PRODUCTS_VIEW: "products:view",
  PRODUCTS_CREATE: "products:create",
  PRODUCTS_EDIT: "products:edit",
  PRODUCTS_DELETE: "products:delete",

  // Commandes
  ORDERS_VIEW: "orders:view",
  ORDERS_CREATE: "orders:create",
  ORDERS_EDIT: "orders:edit",
  ORDERS_DELETE: "orders:delete",

  // Stock
  STOCK_VIEW: "stock:view",
  STOCK_MOVEMENTS: "stock:movements",

  // Clients
  CUSTOMERS_VIEW: "customers:view",
  CUSTOMERS_MANAGE: "customers:manage",

  // Fournisseurs
  SUPPLIERS_VIEW: "suppliers:view",
  SUPPLIERS_MANAGE: "suppliers:manage",

  // Rapports
  REPORTS_VIEW: "reports:view",
  REPORTS_EXPORT: "reports:export",

  // Administration
  USERS_MANAGE: "users:manage",
  SETTINGS_MANAGE: "settings:manage",

  // Catégories
  CATEGORIES_MANAGE: "categories:manage",
} as const;

export type PermissionValue = (typeof PERMISSIONS)[keyof typeof PERMISSIONS];

export async function getRolePermissions() {
  try {
    const response = await fetch("/api/permissions");
    return await response.json();
  } catch {
    return {
      ADMIN: Object.values(PERMISSIONS),
      MANAGER: [
        PERMISSIONS.PRODUCTS_VIEW,
        PERMISSIONS.PRODUCTS_CREATE,
        PERMISSIONS.PRODUCTS_EDIT,
        PERMISSIONS.ORDERS_VIEW,
        PERMISSIONS.ORDERS_CREATE,
        PERMISSIONS.ORDERS_EDIT,
        PERMISSIONS.STOCK_VIEW,
        PERMISSIONS.STOCK_MOVEMENTS,
        PERMISSIONS.CUSTOMERS_VIEW,
        PERMISSIONS.CUSTOMERS_MANAGE,
        PERMISSIONS.SUPPLIERS_VIEW,
        PERMISSIONS.SUPPLIERS_MANAGE,
        PERMISSIONS.REPORTS_VIEW,
        PERMISSIONS.REPORTS_EXPORT,
        PERMISSIONS.CATEGORIES_MANAGE,
      ],
      EMPLOYEE: [
        PERMISSIONS.PRODUCTS_VIEW,
        PERMISSIONS.ORDERS_VIEW,
        PERMISSIONS.ORDERS_CREATE,
        PERMISSIONS.STOCK_VIEW,
        PERMISSIONS.CUSTOMERS_VIEW,
        PERMISSIONS.SUPPLIERS_VIEW,
      ],
    };
  }
}

export function hasPermission(userRole: string, permission: string): boolean {
  // Utiliser des permissions par défaut pour éviter les appels API synchrones
  const defaultPermissions: Record<string, PermissionValue[]> = {
    ADMIN: Object.values(PERMISSIONS),
    MANAGER: [
      PERMISSIONS.PRODUCTS_VIEW,
      PERMISSIONS.PRODUCTS_CREATE,
      PERMISSIONS.PRODUCTS_EDIT,
      PERMISSIONS.ORDERS_VIEW,
      PERMISSIONS.ORDERS_CREATE,
      PERMISSIONS.ORDERS_EDIT,
      PERMISSIONS.STOCK_VIEW,
      PERMISSIONS.STOCK_MOVEMENTS,
      PERMISSIONS.CUSTOMERS_VIEW,
      PERMISSIONS.CUSTOMERS_MANAGE,
      PERMISSIONS.SUPPLIERS_VIEW,
      PERMISSIONS.SUPPLIERS_MANAGE,
      PERMISSIONS.REPORTS_VIEW,
      PERMISSIONS.REPORTS_EXPORT,
      PERMISSIONS.CATEGORIES_MANAGE,
    ],
    EMPLOYEE: [
      PERMISSIONS.PRODUCTS_VIEW,
      PERMISSIONS.ORDERS_VIEW,
      PERMISSIONS.ORDERS_CREATE,
      PERMISSIONS.STOCK_VIEW,
      PERMISSIONS.CUSTOMERS_VIEW,
      PERMISSIONS.SUPPLIERS_VIEW,
    ],
  };

  const permissions = defaultPermissions[userRole];
  return permissions?.includes(permission as PermissionValue) || false;
}

export function canAccess(userRole: string, permissions: string[]): boolean {
  return permissions.some((permission) => hasPermission(userRole, permission));
}
